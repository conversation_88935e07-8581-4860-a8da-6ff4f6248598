"use client"

import Image from "next/image"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import AnimatedSection from "@/components/animated-section"
import { motion } from "framer-motion"
import { Users, Mountain, Heart, Shield, Compass, Award, MessageSquare, Clock, Zap } from "lucide-react"
import ParallaxSection from "@/components/parallax-section"

export default function AboutPage() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative h-[60vh] flex items-center overflow-hidden">
        <div className="absolute inset-0 z-0">
          <Image
            src="https://images.unsplash.com/photo-1516026672322-bc52d61a55d5?fm=jpg&q=80&w=2000&ixlib=rb-4.1.0"
            alt="Beautiful African landscape with acacia tree during golden hour"
            fill
            className="object-cover brightness-75"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-b from-black/40 via-transparent to-black/40"></div>
        </div>

        {/* Decorative elements */}
        <div className="absolute top-10 left-10 w-32 h-32 border-l-2 border-t-2 border-jarthaz-gold/30 z-10"></div>
        <div className="absolute bottom-10 right-10 w-32 h-32 border-r-2 border-b-2 border-jarthaz-gold/30 z-10"></div>

        <div className="container relative z-10 text-white">
          <motion.div
            className="max-w-2xl space-y-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <div className="inline-block bg-jarthaz-gold/20 backdrop-blur-sm px-4 py-1 rounded-full text-white text-sm font-medium border border-jarthaz-gold/30">
              Our Journey
            </div>
            <h1 className="text-4xl font-bold tracking-tighter sm:text-5xl md:text-6xl text-shadow">
              About <span className="text-jarthaz-gold">Jarthaz Tours</span>
            </h1>
            <p className="text-lg text-white/90 max-w-xl">
              Learn about our story, mission, and commitment to showcasing the beauty of Uganda.
            </p>
          </motion.div>
        </div>

        {/* Wave divider at bottom */}
        <div className="absolute bottom-0 left-0 right-0">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320" className="text-background fill-current">
            <path d="M0,224L48,213.3C96,203,192,181,288,181.3C384,181,480,203,576,202.7C672,203,768,181,864,181.3C960,181,1056,203,1152,208C1248,213,1344,203,1392,197.3L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path>
          </svg>
        </div>
      </section>

      {/* Our Story */}
      <section className="py-20">
        <div className="container">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <AnimatedSection direction="right">
              <ParallaxSection>
                <div className="relative h-[500px] rounded-lg overflow-hidden shadow-xl">
                  <Image
                    src="https://images.unsplash.com/photo-1516026672322-bc52d61a55d5?fm=jpg&q=80&w=2000&ixlib=rb-4.1.0"
                    alt="Beautiful African landscape with acacia tree during golden hour"
                    fill
                    className="object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-jarthaz-black/50 to-transparent"></div>

                  {/* Decorative elements */}
                  <div className="absolute top-6 left-6 w-20 h-20 border-t-2 border-l-2 border-jarthaz-gold/50 rounded-tl-lg"></div>
                  <div className="absolute bottom-6 right-6 w-20 h-20 border-b-2 border-r-2 border-jarthaz-gold/50 rounded-br-lg"></div>

                  <div className="absolute bottom-8 left-8 right-8 bg-white/90 dark:bg-jarthaz-black/90 p-6 rounded-lg backdrop-blur-sm">
                    <h3 className="font-bold text-xl mb-2">Our Journey Began in 2018</h3>
                    <p className="text-muted-foreground">
                      From a small local initiative to a leading tours and travel company in Uganda
                    </p>
                  </div>
                </div>
              </ParallaxSection>
            </AnimatedSection>

            <AnimatedSection direction="left">
              <div>
                <div className="inline-block bg-jarthaz-gold/10 px-4 py-1 rounded-full text-jarthaz-gold text-sm font-medium mb-4">
                  Our Story
                </div>
                <h2 className="text-3xl font-bold tracking-tight mb-6">The Jarthaz Tours Journey</h2>
                <div className="safari-divider w-24 mb-6"></div>
                <div className="space-y-4 text-muted-foreground">
                  <p>
                    Jarthaz Tours began with a simple but powerful idea: to share the incredible beauty and cultural
                    richness of Uganda with the world. Founded by passionate travel enthusiasts, we are committed to
                    showcasing the "Pearl of Africa" as a top-tier destination for travelers seeking authentic,
                    immersive experiences.
                  </p>
                  <p>
                    What started as a small local initiative has grown into a leading tours and travel company, offering
                    expertly curated packages and personalized services. At Jarthaz Tours, we believe that travel is not
                    just about visiting new places but about creating meaningful connections—with nature, culture, and
                    people.
                  </p>
                  <p>
                    Our team consists of experienced guides, travel experts, and local specialists who are deeply
                    knowledgeable about Uganda's landscapes, wildlife, and cultural heritage. This expertise allows us
                    to craft journeys that go beyond the ordinary, revealing the hidden gems and authentic experiences
                    that make Uganda truly special.
                  </p>
                </div>
              </div>
            </AnimatedSection>
          </div>
        </div>
      </section>

      {/* Vision & Mission */}
      <section className="py-20 bg-muted/30 safari-pattern-bg relative overflow-hidden">
        {/* Decorative elements */}
        <div className="absolute top-0 right-0 w-64 h-64 bg-jarthaz-gold/5 rounded-full -translate-y-1/2 translate-x-1/2"></div>
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-jarthaz-gold/5 rounded-full translate-y-1/2 -translate-x-1/2"></div>

        <div className="container relative">
          <AnimatedSection>
            <div className="text-center mb-12">
              <div className="inline-block bg-jarthaz-gold/10 px-4 py-1 rounded-full text-jarthaz-gold text-sm font-medium mb-4">
                Our Purpose
              </div>
              <h2 className="text-3xl font-bold tracking-tight mb-4">Vision & Mission</h2>
              <div className="safari-divider w-24 mx-auto mb-6"></div>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                Our vision and mission guide every aspect of our operations, from the experiences we create to the way
                we interact with our clients and communities.
              </p>
            </div>
          </AnimatedSection>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <AnimatedSection delay={0.2}>
              <motion.div
                className="bg-white dark:bg-jarthaz-black/50 rounded-lg overflow-hidden shadow-md h-full card-hover"
                whileHover={{ y: -5 }}
              >
                <div className="relative h-48">
                  <Image src="/images/wildlife/queen-elizabeth-landscape.jpeg" alt="Vision - Beautiful landscape of Queen Elizabeth National Park" fill className="object-cover" />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="bg-jarthaz-gold/20 backdrop-blur-sm p-4 rounded-full border border-jarthaz-gold/30">
                      <Award className="h-10 w-10 text-white" />
                    </div>
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-2xl font-bold mb-4 text-center">Our Vision</h3>
                  <div className="safari-divider w-16 mx-auto mb-4"></div>
                  <p className="text-muted-foreground text-center">
                    To be the most trusted and innovative travel partner, inspiring unforgettable journeys across Uganda
                    and beyond, while setting new standards for sustainable and responsible tourism in East Africa.
                  </p>
                </div>
              </motion.div>
            </AnimatedSection>

            <AnimatedSection delay={0.3}>
              <motion.div
                className="bg-white dark:bg-jarthaz-black/50 rounded-lg overflow-hidden shadow-md h-full card-hover"
                whileHover={{ y: -5 }}
              >
                <div className="relative h-48">
                  <Image src="/images/wildlife/murchison-falls-dramatic.jpeg" alt="Mission - Dramatic view of Murchison Falls" fill className="object-cover" />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="bg-jarthaz-gold/20 backdrop-blur-sm p-4 rounded-full border border-jarthaz-gold/30">
                      <Compass className="h-10 w-10 text-white" />
                    </div>
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-2xl font-bold mb-4 text-center">Our Mission</h3>
                  <div className="safari-divider w-16 mx-auto mb-4"></div>
                  <p className="text-muted-foreground text-center">
                    To provide exceptional travel experiences that connect our clients to Uganda's natural beauty,
                    cultural heritage, and warm hospitality, while promoting sustainable tourism and community
                    development that benefits local populations and preserves our natural resources.
                  </p>
                </div>
              </motion.div>
            </AnimatedSection>
          </div>
        </div>
      </section>

      {/* Core Values */}
      <section className="py-20">
        <div className="container">
          <AnimatedSection>
            <div className="text-center mb-12">
              <div className="inline-block bg-jarthaz-gold/10 px-4 py-1 rounded-full text-jarthaz-gold text-sm font-medium mb-4">
                What Drives Us
              </div>
              <h2 className="text-3xl font-bold tracking-tight mb-4">Our Core Values</h2>
              <div className="safari-divider w-24 mx-auto mb-6"></div>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                These principles guide every aspect of our operations and interactions, ensuring we deliver exceptional
                experiences while making a positive impact.
              </p>
            </div>
          </AnimatedSection>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {[
              {
                icon: Award,
                title: "Excellence",
                description: "We strive to deliver outstanding services that exceed our clients' expectations.",
                delay: 0.2,
              },
              {
                icon: Mountain,
                title: "Sustainability",
                description:
                  "We are committed to eco-friendly practices and supporting local communities to ensure a positive impact on Uganda's environment and culture.",
                delay: 0.3,
              },
              {
                icon: Shield,
                title: "Integrity",
                description:
                  "Honesty and transparency guide every aspect of our operations, from pricing to customer interactions.",
                delay: 0.4,
              },
              {
                icon: Heart,
                title: "Passion",
                description:
                  "Our love for Uganda's beauty and diversity drives us to create memorable and transformative travel experiences.",
                delay: 0.5,
              },
              {
                icon: Zap,
                title: "Innovation",
                description:
                  "We embrace creativity and adaptability, offering fresh and unique approaches to travel planning.",
                delay: 0.6,
              },
              {
                icon: Users,
                title: "Customer Focus",
                description:
                  "Our clients are at the heart of everything we do. We listen, adapt, and go the extra mile to ensure satisfaction.",
                delay: 0.7,
              },
            ].map((value, index) => (
              <AnimatedSection key={index} delay={value.delay}>
                <motion.div
                  className="bg-white dark:bg-jarthaz-black/50 p-6 rounded-lg shadow-md h-full card-hover"
                  whileHover={{ y: -5 }}
                >
                  <div className="flex flex-col items-center text-center">
                    <div className="bg-jarthaz-gold/10 p-4 rounded-full w-fit mb-4">
                      <value.icon className="h-8 w-8 text-jarthaz-gold" />
                    </div>
                    <h3 className="text-xl font-bold mb-2">{value.title}</h3>
                    <div className="safari-divider w-12 my-3"></div>
                    <p className="text-muted-foreground">{value.description}</p>
                  </div>
                </motion.div>
              </AnimatedSection>
            ))}
          </div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="py-20 bg-muted/30 safari-pattern-bg">
        <div className="container">
          <AnimatedSection>
            <div className="text-center mb-12">
              <div className="inline-block bg-jarthaz-gold/10 px-4 py-1 rounded-full text-jarthaz-gold text-sm font-medium mb-4">
                Our Difference
              </div>
              <h2 className="text-3xl font-bold tracking-tight mb-4">Why Choose Jarthaz Tours?</h2>
              <div className="safari-divider w-24 mx-auto mb-6"></div>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                We're dedicated to making your Ugandan adventure unforgettable through our commitment to quality,
                expertise, and personalized service.
              </p>
            </div>
          </AnimatedSection>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              {
                icon: MessageSquare,
                title: "Customized Itineraries",
                description: "We tailor every package to meet your interests and budget.",
                delay: 0.2,
              },
              {
                icon: Users,
                title: "Experienced Guides",
                description: "Our knowledgeable team ensures a safe and enriching experience.",
                delay: 0.3,
              },
              {
                icon: Shield,
                title: "Sustainable Tourism",
                description: "We prioritize eco-friendly practices and community involvement.",
                delay: 0.4,
              },
              {
                icon: Clock,
                title: "24/7 Support",
                description: "We're here to assist you every step of the way.",
                delay: 0.5,
              },
            ].map((feature, index) => (
              <AnimatedSection key={index} delay={feature.delay}>
                <motion.div
                  className="bg-white dark:bg-jarthaz-black/50 p-6 rounded-lg shadow-md h-full"
                  whileHover={{ y: -5, backgroundColor: "rgba(212, 175, 55, 0.05)" }}
                >
                  <div className="flex flex-col items-center text-center">
                    <div className="bg-jarthaz-gold/10 p-3 rounded-full w-fit mb-4">
                      <feature.icon className="h-6 w-6 text-jarthaz-gold" />
                    </div>
                    <h3 className="text-lg font-bold mb-2">{feature.title}</h3>
                    <p className="text-muted-foreground text-sm">{feature.description}</p>
                  </div>
                </motion.div>
              </AnimatedSection>
            ))}
          </div>
        </div>
      </section>

      {/* Image Gallery Section */}
      <section className="py-16 bg-stone-50">
        <div className="container">
          <AnimatedSection>
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4">Our Uganda, Your Adventure</h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                From thrilling wildlife encounters to serene landscapes, we bring you the very best of Uganda.
              </p>
            </div>
          </AnimatedSection>

          <div className="grid grid-cols-12 gap-6">
            <div className="col-span-12 md:col-span-8">
              <div className="relative h-[400px] rounded-xl overflow-hidden group">
                <Image
                  src="/images/wildlife/elephant-family-savanna.jpeg"
                  alt="Elephant family in the savanna"
                  fill
                  className="object-cover transition-transform group-hover:scale-105 duration-700"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent">
                  <div className="absolute bottom-0 p-6">
                    <h3 className="text-white text-2xl font-bold mb-2">Wildlife Safaris</h3>
                    <p className="text-white/90">Unforgettable encounters with Africa's majestic wildlife</p>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-span-12 md:col-span-4">
              <div className="grid grid-rows-2 gap-6 h-full">
                <div className="relative rounded-xl overflow-hidden group">
                  <Image
                    src="/images/adventures/horseback-safari.jpeg"
                    alt="Horseback safari adventure"
                    fill
                    className="object-cover transition-transform group-hover:scale-105 duration-700"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent">
                    <div className="absolute bottom-0 p-4">
                      <h3 className="text-white text-lg font-bold">Adventure Tours</h3>
                    </div>
                  </div>
                </div>
                <div className="relative rounded-xl overflow-hidden group">
                  <Image
                    src="/images/gorillas/gorilla-family-baby.jpeg"
                    alt="Gorilla family with baby"
                    fill
                    className="object-cover transition-transform group-hover:scale-105 duration-700"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent">
                    <div className="absolute bottom-0 p-4">
                      <h3 className="text-white text-lg font-bold">Gorilla Encounters</h3>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
            <div className="relative h-[300px] rounded-xl overflow-hidden group">
              <Image
                src="/images/adventures/rafting-nile.jpeg"
                alt="White water rafting on the Nile"
                fill
                className="object-cover transition-transform group-hover:scale-105 duration-700"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent">
                <div className="absolute bottom-0 p-6">
                  <h3 className="text-white text-xl font-bold mb-2">Adventure Sports</h3>
                  <p className="text-white/90">Thrilling activities for the adventurous soul</p>
                </div>
              </div>
            </div>
            <div className="relative h-[300px] rounded-xl overflow-hidden group">
              <Image
                src="/images/wildlife/crater-lake.jpeg"
                alt="Beautiful crater lake view"
                fill
                className="object-cover transition-transform group-hover:scale-105 duration-700"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent">
                <div className="absolute bottom-0 p-6">
                  <h3 className="text-white text-xl font-bold mb-2">Natural Wonders</h3>
                  <p className="text-white/90">Discover Uganda's breathtaking landscapes</p>
                </div>
              </div>
            </div>
            <div className="relative h-[300px] rounded-xl overflow-hidden group">
              <Image
                src="/images/honeymoon/main-building-terrace-ready-for-dinner.jpg"
                alt="Luxury dining experience"
                fill
                className="object-cover transition-transform group-hover:scale-105 duration-700"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent">
                <div className="absolute bottom-0 p-6">
                  <h3 className="text-white text-xl font-bold mb-2">Luxury Experiences</h3>
                  <p className="text-white/90">Indulge in world-class comfort and service</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-jarthaz-black text-white relative overflow-hidden">
        <div className="absolute inset-0 z-0">
          <Image
            src="/images/wildlife/murchison-falls-aerial.jpeg"
            alt="Aerial view of Murchison Falls"
            fill
            className="object-cover opacity-30"
          />
        </div>

        {/* Decorative elements */}
        <div className="absolute top-0 left-0 w-full h-12 bg-gradient-to-b from-background to-transparent z-10"></div>
        <div className="absolute bottom-0 left-0 w-full h-12 bg-gradient-to-t from-background to-transparent z-10"></div>
        <div className="absolute top-12 right-12 w-24 h-24 border-r-2 border-t-2 border-jarthaz-gold/30 z-10"></div>
        <div className="absolute bottom-12 left-12 w-24 h-24 border-l-2 border-b-2 border-jarthaz-gold/30 z-10"></div>

        <div className="container text-center relative z-10">
          <AnimatedSection>
            <div className="inline-block bg-jarthaz-gold/20 backdrop-blur-sm px-4 py-1 rounded-full text-white text-sm font-medium mb-6">
              Start Your Journey
            </div>
            <h2 className="text-3xl md:text-4xl font-bold tracking-tight mb-4 text-jarthaz-gold">
              Ready to Explore Uganda with Us?
            </h2>
            <div className="safari-divider w-24 mx-auto my-6"></div>
            <p className="mb-8 max-w-2xl mx-auto text-white/90">
              Let's craft your perfect Ugandan adventure together. Contact us for inquiries, bookings, and personalized
              packages.
            </p>
            <Button size="lg" variant="safari" asChild className="btn-shimmer">
              <Link href="/contact">Contact Us Today</Link>
            </Button>
          </AnimatedSection>
        </div>
      </section>
    </div>
  )
}
