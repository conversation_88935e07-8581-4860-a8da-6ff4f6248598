import type { LucideIcon } from "lucide-react"

interface SafariIconProps {
  icon: LucideIcon
  title: string
  description: string
}

export default function SafariIcon({ icon: Icon, title, description }: SafariIconProps) {
  return (
    <div className="flex flex-col items-center text-center p-6 border rounded-lg hover:shadow-md transition-shadow bg-white dark:bg-jarthaz-black/50">
      <div className="bg-jarthaz-gold/10 p-4 rounded-full mb-4 group hover:bg-jarthaz-gold/20 transition-colors">
        <Icon className="h-8 w-8 text-jarthaz-gold float-icon" />
      </div>
      <h3 className="text-xl font-semibold mb-2">{title}</h3>
      <p className="text-muted-foreground">{description}</p>
    </div>
  )
}
