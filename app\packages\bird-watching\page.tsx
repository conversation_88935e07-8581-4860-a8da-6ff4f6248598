"use client"

import Image from "next/image"
import Link from "next/link"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Check, ArrowLeft, Star, MapPin, Calendar, Clock, Users, ChevronRight, Binoculars } from "lucide-react"
import AnimatedSection from "@/components/animated-section"
import SafariDivider from "@/components/safari-divider"
import type { JSX, ReactElement } from 'react'
import type { HTMLAttributes } from 'react'

interface BirdLocation {
  name: string;
  image: string;
  species: string;
  description: string;
}

const birdLocations: BirdLocation[] = [
  {
    name: "Kibale Forest National Park",
    image: "/images/birds/Kibale_forest_park__-great-blue-turaco.webp",
    species: "Great Blue Turaco",
    description: "Home to the vibrant Great Blue Turaco and numerous forest birds."
  },
  {
    name: "Bangweulu Wetlands",
    image: "/images/birds/shoebill-in-bangweulu_credit-african-parks-mana-meadows-3.jpg",
    species: "Shoebill Stork",
    description: "One of the best places to spot the rare and prehistoric-looking Shoebill."
  },
  {
    name: "Mabamba Swamp",
    image: "/images/birds/boris-smokrovic-DPXytK8Z59Y-unsplash-scaled.jpg",
    species: "African Jacana",
    description: "A paradise for water birds and home to the elegant African Jacana."
  },
  {
    name: "Queen Elizabeth National Park",
    image: "/images/birds/vincent-van-zalinge-vUNQaTtZeOo-unsplash-scaled.jpg",
    species: "African Fish Eagle",
    description: "Spot majestic Fish Eagles and numerous waterbirds along the Kazinga Channel."
  }
]

export default function BirdWatchingPage(): ReactElement<HTMLAttributes<HTMLElement>> {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative h-[70vh] flex items-center overflow-hidden">
        <div className="absolute inset-0 z-0">
          <Image
            src="/images/birds/vincent-van-zalinge-vUNQaTtZeOo-unsplash-scaled.jpg"
            alt="Majestic African Fish Eagle in Uganda"
            fill
            className="object-cover brightness-[0.7] scale-110 transition-all duration-10000 animate-slow-zoom"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-b from-black/30 via-transparent to-black/60" />
        </div>

        {/* Decorative Elements */}
        <div className="absolute top-20 left-10 w-32 h-32 border-2 border-[#D4AF37]/20 rounded-full" />
        <div className="absolute bottom-20 right-10 w-48 h-48 border-2 border-[#D4AF37]/20 rounded-full" />
        <div className="absolute top-40 right-20 w-16 h-16 border-2 border-[#D4AF37]/20 rounded-full" />

        <div className="container relative z-10 text-white">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-3xl space-y-4"
          >
            <Link
              href="/destinations"
              className="inline-flex items-center text-white/90 hover:text-[#D4AF37] transition-colors mb-4 group"
            >
              <ArrowLeft size={16} className="mr-2 group-hover:-translate-x-1 transition-transform" />
              Back to Destinations
            </Link>

            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.8 }}
              className="backdrop-blur-sm bg-black/30 p-6 rounded-lg border border-white/10 max-w-2xl"
            >
              <h1 className="text-4xl font-bold tracking-tighter sm:text-5xl md:text-6xl mb-4">
                Bird Watching <span className="text-[#D4AF37]">Expeditions</span>
              </h1>
              <p className="text-xl text-white/90">
                Uganda is a bird-watcher's paradise, home to over 1,000 bird species including the rare shoebill stork.
              </p>
              <div className="flex items-center mt-4 space-x-1">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star key={star} size={18} className="fill-[#D4AF37] text-[#D4AF37]" />
                ))}
                <span className="ml-2 text-sm text-white/90">Based on 48 reviews</span>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-16">
        <div className="container">
          <AnimatedSection>
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight mb-6 flex items-center justify-center">
                <Binoculars className="w-8 h-8 text-[#D4AF37] mr-3" />
                Discover Uganda's Bird Paradise
              </h2>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                With over 1,000 bird species recorded within its borders, Uganda offers one of Africa's finest bird watching experiences. From the rare Shoebill to the vibrant Great Blue Turaco, our guided tours take you to the best birding spots in the country.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {birdLocations.map((location, index) => (
                <motion.div
                  key={location.name}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="group relative overflow-hidden rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300"
                >
                  <div className="relative h-64 overflow-hidden">
                    <Image
                      src={location.image}
                      alt={`${location.species} at ${location.name}`}
                      fill
                      className="object-cover transition-transform duration-700 group-hover:scale-110"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/30 to-transparent opacity-60 group-hover:opacity-70 transition-opacity" />
                  </div>
                  <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
                    <h3 className="text-xl font-bold mb-2">{location.name}</h3>
                    <p className="text-[#D4AF37] font-semibold mb-2">{location.species}</p>
                    <p className="text-sm text-white/90">{location.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </AnimatedSection>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-primary text-primary-foreground relative overflow-hidden">
        <div className="absolute inset-0 bg-[url('/placeholder.svg?height=200&width=200')] bg-repeat opacity-10 mix-blend-overlay" />
        <div className="container relative z-10 text-center">
          <AnimatedSection>
            <h2 className="text-3xl font-bold tracking-tight mb-4">Ready to Start Your Bird Watching Adventure?</h2>
            <p className="mb-8 max-w-2xl mx-auto text-primary-foreground/90">
              Join us on an unforgettable journey through Uganda's diverse landscapes to discover its incredible bird life.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                size="lg"
                variant="secondary"
                className="bg-[#D4AF37] hover:bg-[#D4AF37]/90 text-black border-[#D4AF37]"
                asChild
              >
                <Link href="/contact">Book Your Tour</Link>
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="bg-transparent text-white border-white hover:bg-white hover:text-primary"
                asChild
              >
                <Link href="/destinations">View Other Packages</Link>
              </Button>
            </div>
          </AnimatedSection>
        </div>
      </section>
    </div>
  )
}
