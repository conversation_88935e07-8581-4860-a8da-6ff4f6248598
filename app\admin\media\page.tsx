import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Search, Upload, Grid, List, Filter, Edit, Trash2, ImageIcon } from "lucide-react"
import Image from "next/image"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

// Mock data for media items
const mediaItems = Array.from({ length: 24 }, (_, i) => ({
  id: `image-${i + 1}`,
  name: `Image ${i + 1}.jpg`,
  type: "image",
  size: `${Math.floor(Math.random() * 1000) + 100} KB`,
  dimensions: "1920 × 1080",
  uploaded: "2023-05-12",
  url: "/placeholder.svg?height=400&width=600",
}))

export default function MediaPage() {
  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Media Library</h2>
        <Button>
          <Upload className="mr-2 h-4 w-4" /> Upload
        </Button>
      </div>

      <div className="flex flex-col gap-4">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input type="search" placeholder="Search media..." className="pl-8" />
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline">
              <Filter className="mr-2 h-4 w-4" /> Filter
            </Button>
            <Tabs defaultValue="grid" className="w-[120px]">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="grid">
                  <Grid className="h-4 w-4" />
                </TabsTrigger>
                <TabsTrigger value="list">
                  <List className="h-4 w-4" />
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </div>

        <Tabs defaultValue="grid" className="w-full">
          <TabsContent value="grid" className="mt-0">
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
              {mediaItems.map((item) => (
                <Card key={item.id} className="overflow-hidden group">
                  <div className="relative aspect-square">
                    <Image src={item.url || "/placeholder.svg"} alt={item.name} fill className="object-cover" />
                    <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                      <Button variant="secondary" size="icon" className="h-8 w-8">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button variant="destructive" size="icon" className="h-8 w-8">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  <CardContent className="p-2">
                    <p className="text-xs truncate" title={item.name}>
                      {item.name}
                    </p>
                    <p className="text-xs text-muted-foreground">{item.size}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="list" className="mt-0">
            <div className="border rounded-md">
              <div className="grid grid-cols-12 gap-4 p-4 border-b bg-muted/50 font-medium text-sm">
                <div className="col-span-5">Name</div>
                <div className="col-span-2">Type</div>
                <div className="col-span-2">Size</div>
                <div className="col-span-2">Uploaded</div>
                <div className="col-span-1">Actions</div>
              </div>
              {mediaItems.map((item) => (
                <div key={item.id} className="grid grid-cols-12 gap-4 p-4 border-b items-center text-sm">
                  <div className="col-span-5 flex items-center gap-2">
                    <div className="relative h-10 w-10 rounded overflow-hidden flex-shrink-0">
                      <Image src={item.url || "/placeholder.svg"} alt={item.name} fill className="object-cover" />
                    </div>
                    <span className="truncate">{item.name}</span>
                  </div>
                  <div className="col-span-2 flex items-center gap-1">
                    <ImageIcon className="h-4 w-4 text-muted-foreground" />
                    <span>Image</span>
                  </div>
                  <div className="col-span-2">{item.size}</div>
                  <div className="col-span-2">{item.uploaded}</div>
                  <div className="col-span-1">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <Edit className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>Edit</DropdownMenuItem>
                        <DropdownMenuItem>Copy URL</DropdownMenuItem>
                        <DropdownMenuItem className="text-destructive">Delete</DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
