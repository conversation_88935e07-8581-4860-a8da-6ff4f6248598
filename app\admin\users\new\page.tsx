import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { ArrowLeft, Save } from "lucide-react"
import Link from "next/link"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"

export default function NewUserPage() {
  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" asChild>
            <Link href="/admin/users">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h2 className="text-3xl font-bold tracking-tight">Add New User</h2>
        </div>
        <Button>
          <Save className="mr-2 h-4 w-4" /> Save User
        </Button>
      </div>

      <div className="grid gap-6 md:grid-cols-[2fr_1fr]">
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>User Information</CardTitle>
              <CardDescription>Add details for the new user account.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="name">Full Name</Label>
                <Input id="name" placeholder="Enter user's full name" />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="email">Email Address</Label>
                <Input id="email" type="email" placeholder="Enter user's email address" />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="password">Temporary Password</Label>
                <Input id="password" type="password" placeholder="Create a temporary password" />
                <p className="text-sm text-muted-foreground">
                  The user will be prompted to change this password on first login.
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Permissions</CardTitle>
              <CardDescription>Set what this user can access in the admin dashboard.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="role">User Role</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="admin">Administrator</SelectItem>
                    <SelectItem value="editor">Editor</SelectItem>
                    <SelectItem value="viewer">Viewer</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-sm text-muted-foreground">
                  The role determines the user's default permissions in the system.
                </p>
              </div>

              <Separator className="my-4" />

              <div className="space-y-4">
                <h3 className="text-sm font-medium">Custom Permissions</h3>
                <p className="text-sm text-muted-foreground">
                  Customize specific permissions for this user beyond their role defaults.
                </p>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="perm-destinations" className="flex-1">
                      Manage Destinations
                    </Label>
                    <Switch id="perm-destinations" />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="perm-packages" className="flex-1">
                      Manage Packages
                    </Label>
                    <Switch id="perm-packages" />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="perm-content" className="flex-1">
                      Edit Website Content
                    </Label>
                    <Switch id="perm-content" />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="perm-media" className="flex-1">
                      Upload Media
                    </Label>
                    <Switch id="perm-media" />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="perm-inquiries" className="flex-1">
                      Respond to Inquiries
                    </Label>
                    <Switch id="perm-inquiries" />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="perm-users" className="flex-1">
                      Manage Users
                    </Label>
                    <Switch id="perm-users" />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="perm-settings" className="flex-1">
                      Change System Settings
                    </Label>
                    <Switch id="perm-settings" />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Account Settings</CardTitle>
              <CardDescription>Configure additional account options.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="active-status">Account Active</Label>
                  <p className="text-sm text-muted-foreground">User can log in when active</p>
                </div>
                <Switch id="active-status" defaultChecked />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="force-reset">Force Password Reset</Label>
                  <p className="text-sm text-muted-foreground">User must change password on first login</p>
                </div>
                <Switch id="force-reset" defaultChecked />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="email-notifications">Email Notifications</Label>
                  <p className="text-sm text-muted-foreground">Send system notifications via email</p>
                </div>
                <Switch id="email-notifications" defaultChecked />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Notification Settings</CardTitle>
              <CardDescription>Configure what notifications the user receives.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="notify-inquiries" className="flex-1">
                  New Inquiries
                </Label>
                <Switch id="notify-inquiries" defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <Label htmlFor="notify-bookings" className="flex-1">
                  New Bookings
                </Label>
                <Switch id="notify-bookings" defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <Label htmlFor="notify-comments" className="flex-1">
                  Website Comments
                </Label>
                <Switch id="notify-comments" />
              </div>
              <div className="flex items-center justify-between">
                <Label htmlFor="notify-system" className="flex-1">
                  System Updates
                </Label>
                <Switch id="notify-system" />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
