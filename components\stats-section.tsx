"use client"

import { motion } from "framer-motion"
import AnimatedCounter from "@/components/animated-counter"
import { Users, Calendar, Award, MapPin } from "lucide-react"

export default function StatsSection() {
  const stats = [
    {
      icon: Users,
      value: 5000,
      suffix: "+",
      label: "Happy Travelers",
      delay: 0,
    },
    {
      icon: Calendar,
      value: 12,
      suffix: "+",
      label: "Years of Experience",
      delay: 0.2,
    },
    {
      icon: Award,
      value: 98,
      suffix: "%",
      label: "Satisfaction Rate",
      delay: 0.4,
    },
    {
      icon: MapPin,
      value: 25,
      suffix: "+",
      label: "Destinations",
      delay: 0.6,
    },
  ]

  return (
    <div className="py-16 bg-muted/50 safari-pattern-bg relative overflow-hidden">
      <div className="container">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: stat.delay }}
              viewport={{ once: true, margin: "-100px" }}
              className="flex flex-col items-center text-center"
            >
              <div className="w-16 h-16 rounded-full bg-jarthaz-gold/20 flex items-center justify-center mb-4">
                <stat.icon size={32} className="text-jarthaz-gold" />
              </div>
              <div className="text-3xl font-bold mb-2">
                <AnimatedCounter end={stat.value} duration={2} delay={stat.delay} suffix={stat.suffix} />
              </div>
              <div className="text-muted-foreground">{stat.label}</div>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  )
}
