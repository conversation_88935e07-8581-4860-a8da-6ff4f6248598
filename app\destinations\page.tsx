"use client"

import Image from "next/image"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { MapPin, ExternalLink, ChevronRight, Star } from "lucide-react"
import AnimatedSection from "@/components/animated-section"
import PackageCard from "@/components/package-card"
import { motion } from "framer-motion"
import { useState } from "react"

export default function DestinationsPage() {
  const [activeCategory, setActiveCategory] = useState("all")

  // Tour packages data
  const tourPackages = [
    {
      id: "gorilla-trekking",
      title: "Gorilla Trekking Adventures",
      description: "A once-in-a-lifetime journey to meet Uganda's gentle giants in their natural habitat.",
      image: "/images/gorillas/gorilla-family.jpeg",
      alt: "Gorilla Trekking",
      category: "wildlife",
      duration: "3-5 days",
      groupSize: "2-8",
    },
    {
      id: "wildlife-safaris",
      title: "Wildlife Safaris",
      description: "Experience the thrill of Uganda's wildlife in its natural splendor.",
      image: "/images/wildlife/elephant-family-savanna.jpeg",
      alt: "Wildlife Safari",
      category: "wildlife",
      duration: "4-7 days",
      groupSize: "2-10",
    },
    {
      id: "adventure-escapades",
      title: "Adventure Escapades",
      description: "For adrenaline seekers, Uganda offers endless excitement.",
      image: "/images/adventures/rafting-waves.jpeg",
      alt: "Adventure Activities - White Water Rafting",
      category: "adventure",
      duration: "2-5 days",
      groupSize: "2-12",
    },
    {
      id: "cultural-tours",
      title: "Cultural and Heritage Tours",
      description: "Immerse yourself in Uganda's rich cultural tapestry.",
      image: "https://images.unsplash.com/photo-1515657834497-26509e295154?fm=jpg&q=80&w=2000&ixlib=rb-4.1.0",
      alt: "Cultural Tours - Traditional African Dancers",
      category: "culture",
      duration: "3-6 days",
      groupSize: "2-15",
    },
    {
      id: "bird-watching",
      title: "Bird Watching Expeditions",
      description: "Uganda is a bird-watcher's paradise, home to over 1,000 bird species.",
      image: "/images/birds/vincent-van-zalinge-vUNQaTtZeOo-unsplash-scaled.jpg",
      alt: "Bird Watching - African Fish Eagle",
      category: "wildlife",
      duration: "4-8 days",
      groupSize: "2-8",
    },
    {
      id: "honeymoon-getaways",
      title: "Honeymoon Getaways",
      description: "Celebrate your love with romantic escapes in Uganda's serene locations.",
      image: "/images/honeymoon/pool.jpg",
      alt: "Honeymoon Getaway - Luxury Resort Pool",
      category: "leisure",
      duration: "5-10 days",
      groupSize: "2",
    },
  ]

  // Destinations data
  const destinations = [
    {
      name: "Bwindi Impenetrable National Park",
      type: "National Park",
      image: "/images/gorillas/gorilla-closeup.jpeg",
      description:
        "Home to half of the world's mountain gorilla population, this UNESCO World Heritage Site promises a magical encounter with nature.",
      activities: ["Gorilla trekking", "Forest hikes", "Bird watching", "Community visits"],
      rating: 4.9,
      reviews: 124,
    },
    {
      name: "Queen Elizabeth National Park",
      type: "National Park",
      image: "/images/wildlife/queen-elizabeth-landscape.jpeg",
      description: "Uganda's most popular savannah park, offering diverse ecosystems and incredible wildlife.",
      activities: [
        "Game drives",
        "Boat cruises along the Kazinga Channel",
        "Chimpanzee tracking in Kyambura Gorge",
        "Bird watching",
      ],
      rating: 4.8,
      reviews: 156,
    },
    {
      name: "Murchison Falls National Park",
      type: "National Park",
      image: "/images/wildlife/murchison-falls-dramatic.jpeg",
      description:
        "Witness the dramatic power of the Nile as it forces its way through a narrow gorge, creating the world-famous falls.",
      activities: ["Game drives", "Nile river cruises", "Hikes to the top of the falls", "Bird watching"],
      rating: 4.7,
      reviews: 98,
    },
    {
      name: "Kidepo Valley National Park",
      type: "National Park",
      image: "/images/wildlife/giraffe-herd-path.jpeg",
      description: "A hidden gem in Uganda, known for its rugged beauty and untouched wilderness.",
      activities: [
        "Exploring Narus Valley",
        "Cultural visits to the Karamojong villages",
        "Wildlife spotting",
        "Photography",
      ],
      rating: 4.9,
      reviews: 76,
    },
    {
      name: "Lake Bunyonyi",
      type: "Lake",
      image: "/images/wildlife/crater-lake.jpeg",
      description:
        'Known as the "place of many little birds," this lake boasts serene beauty and is dotted with 29 islands.',
      activities: ["Canoeing", "Island hopping", "Relaxing by the lake", "Bird watching"],
      rating: 4.6,
      reviews: 112,
    },
    {
      name: "Jinja - The Source of the Nile",
      type: "Adventure Hub",
      image: "/images/adventures/river-tubing.webp",
      description: "The adventure capital of East Africa, where the world's longest river begins.",
      activities: ["White-water rafting", "Bungee jumping", "Sunset boat cruises", "Kayaking and tubing"],
      rating: 4.7,
      reviews: 143,
    },
  ]

  // Filter packages by category
  const filteredPackages =
    activeCategory === "all" ? tourPackages : tourPackages.filter((pkg) => pkg.category === activeCategory)

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative h-[70vh] flex items-center overflow-hidden">
        <div className="absolute inset-0 z-0">
          <Image
            src="/images/wildlife/murchison-falls-aerial.jpeg"
            alt="Uganda landscape"
            fill
            className="object-cover"
            priority
          />
          <div className="hero-overlay"></div>
        </div>

        {/* Safari vehicle animation */}
        <div className="safari-vehicle"></div>

        <div className="container relative z-10 text-white">
          <motion.div
            className="max-w-2xl space-y-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <div className="inline-block bg-jarthaz-gold/20 backdrop-blur-sm px-4 py-1 rounded-full text-white text-sm font-medium border border-jarthaz-gold/30">
              Explore Uganda's Wonders
            </div>
            <h1 className="text-4xl font-bold tracking-tighter sm:text-5xl md:text-6xl text-shadow">
              Our <span className="text-jarthaz-gold">Destinations</span>
            </h1>
            <p className="text-lg text-white/90 max-w-xl">
              Discover the Pearl of Africa through our carefully curated tour packages and breathtaking destinations.
            </p>
            <div className="flex flex-wrap gap-4 pt-4">
              <Button asChild variant="safari" size="lg" className="btn-shimmer">
                <Link href="#packages">View Tour Packages</Link>
              </Button>
              <Button
                asChild
                variant="outline"
                size="lg"
                className="bg-white/10 backdrop-blur-sm border-white/20 text-white hover:bg-white/20 hover:text-white"
              >
                <Link href="#destinations">Explore Destinations</Link>
              </Button>
            </div>
          </motion.div>
        </div>

        {/* Decorative elements */}
        <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-background to-transparent z-10"></div>
        <div className="absolute bottom-10 right-10 w-32 h-32 border-r-2 border-b-2 border-jarthaz-gold/30 z-10"></div>
        <div className="absolute top-10 left-10 w-32 h-32 border-l-2 border-t-2 border-jarthaz-gold/30 z-10"></div>
      </section>

      {/* Tour Packages */}
      <section id="packages" className="py-20">
        <div className="container">
          <AnimatedSection>
            <div className="flex flex-col items-center text-center mb-12">
              <div className="inline-block bg-jarthaz-gold/10 px-4 py-1 rounded-full text-jarthaz-gold text-sm font-medium mb-4">
                Our Tour Packages
              </div>
              <h2 className="section-heading text-4xl md:text-5xl">Unforgettable Experiences</h2>
              <div className="safari-divider w-24 my-6"></div>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                From wildlife safaris to cultural experiences, we offer a wide range of packages to suit every traveler.
                Click on any package to learn more.
              </p>
            </div>
          </AnimatedSection>

          {/* Category filter */}
          <AnimatedSection delay={0.2}>
            <div className="flex flex-wrap justify-center gap-2 mb-12">
              <Button
                variant={activeCategory === "all" ? "safari" : "outline"}
                size="sm"
                onClick={() => setActiveCategory("all")}
                className="rounded-full"
              >
                All Packages
              </Button>
              <Button
                variant={activeCategory === "wildlife" ? "safari" : "outline"}
                size="sm"
                onClick={() => setActiveCategory("wildlife")}
                className="rounded-full"
              >
                Wildlife
              </Button>
              <Button
                variant={activeCategory === "adventure" ? "safari" : "outline"}
                size="sm"
                onClick={() => setActiveCategory("adventure")}
                className="rounded-full"
              >
                Adventure
              </Button>
              <Button
                variant={activeCategory === "culture" ? "safari" : "outline"}
                size="sm"
                onClick={() => setActiveCategory("culture")}
                className="rounded-full"
              >
                Cultural
              </Button>
              <Button
                variant={activeCategory === "leisure" ? "safari" : "outline"}
                size="sm"
                onClick={() => setActiveCategory("leisure")}
                className="rounded-full"
              >
                Leisure
              </Button>
            </div>
          </AnimatedSection>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredPackages.map((pkg, index) => (
              <PackageCard
                key={pkg.id}
                {...pkg}
                delay={0.2 + index * 0.1}
                duration={pkg.duration}
                groupSize={pkg.groupSize}
              />
            ))}
          </div>
        </div>
      </section>

      {/* Divider */}
      <div className="container py-8">
        <div className="safari-divider"></div>
      </div>

      {/* Popular Destinations */}
      <section id="destinations" className="py-20 safari-pattern-bg relative overflow-hidden">
        {/* Decorative elements */}
        <div className="absolute top-0 right-0 w-64 h-64 bg-jarthaz-gold/5 rounded-full -translate-y-1/2 translate-x-1/2"></div>
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-jarthaz-gold/5 rounded-full translate-y-1/2 -translate-x-1/2"></div>

        <div className="container relative">
          <AnimatedSection>
            <div className="flex flex-col items-center text-center mb-12">
              <div className="inline-block bg-jarthaz-gold/10 px-4 py-1 rounded-full text-jarthaz-gold text-sm font-medium mb-4">
                Explore Uganda
              </div>
              <h2 className="section-heading text-4xl md:text-5xl">Breathtaking Destinations</h2>
              <div className="safari-divider w-24 my-6"></div>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                From breathtaking landscapes to vibrant cultures, Uganda offers a treasure trove of experiences waiting
                to be discovered.
              </p>
            </div>
          </AnimatedSection>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {destinations.map((destination, index) => (
              <AnimatedSection key={index} delay={0.2 + index * 0.1}>
                <div className="bg-white dark:bg-jarthaz-black/50 rounded-lg overflow-hidden shadow-md h-full flex flex-col card-hover">
                  <div className="relative h-48 image-hover-zoom">
                    <Image
                      src={destination.image}
                      alt={destination.name}
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>

                    {/* Rating badge */}
                    <div className="absolute top-4 right-4 bg-white/90 dark:bg-jarthaz-black/90 backdrop-blur-sm px-2 py-1 rounded-full flex items-center gap-1">
                      <Star size={14} className="text-jarthaz-gold fill-jarthaz-gold" />
                      <span className="text-sm font-medium">{destination.rating}</span>
                      <span className="text-xs text-muted-foreground">({destination.reviews})</span>
                    </div>
                  </div>

                  <div className="p-5 flex-grow flex flex-col">
                    <div className="flex items-center gap-2 mb-2">
                      <MapPin size={16} className="text-jarthaz-gold" />
                      <span className="text-sm text-muted-foreground">{destination.type}</span>
                    </div>
                    <h3 className="text-xl font-bold mb-3">{destination.name}</h3>
                    <p className="text-muted-foreground text-sm mb-4">{destination.description}</p>

                    <div className="mt-auto">
                      <h4 className="text-sm font-medium mb-2">Top Activities:</h4>
                      <ul className="text-sm text-muted-foreground space-y-1 mb-4">
                        {destination.activities.map((activity, idx) => (
                          <li key={idx} className="flex items-start gap-2">
                            <ChevronRight size={14} className="text-jarthaz-gold mt-1 flex-shrink-0" />
                            <span>{activity}</span>
                          </li>
                        ))}
                      </ul>

                      <Button variant="outline" className="w-full group" asChild>
                        <Link href="/contact">
                          Inquire About This Destination
                          <ExternalLink size={16} className="ml-2 group-hover:translate-x-1 transition-transform" />
                        </Link>
                      </Button>
                    </div>
                  </div>
                </div>
              </AnimatedSection>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-jarthaz-black text-white relative overflow-hidden">
        <div className="absolute inset-0 z-0">
          <Image
            src="/images/wildlife/giraffe-herd-path.jpeg"
            alt="Uganda landscape with giraffes"
            fill
            className="object-cover opacity-30"
          />
        </div>

        {/* Decorative elements */}
        <div className="absolute top-0 left-0 w-full h-12 bg-gradient-to-b from-background to-transparent z-10"></div>
        <div className="absolute bottom-0 left-0 w-full h-12 bg-gradient-to-t from-background to-transparent z-10"></div>
        <div className="absolute top-12 right-12 w-24 h-24 border-r-2 border-t-2 border-jarthaz-gold/30 z-10"></div>
        <div className="absolute bottom-12 left-12 w-24 h-24 border-l-2 border-b-2 border-jarthaz-gold/30 z-10"></div>

        <div className="container text-center relative z-10">
          <AnimatedSection>
            <div className="inline-block bg-jarthaz-gold/20 backdrop-blur-sm px-4 py-1 rounded-full text-white text-sm font-medium mb-6">
              Start Your Journey
            </div>
            <h2 className="text-3xl md:text-4xl font-bold tracking-tight mb-4 text-jarthaz-gold">
              Plan Your Adventure Today
            </h2>
            <div className="safari-divider w-24 mx-auto my-6"></div>
            <p className="mb-8 max-w-2xl mx-auto text-white/90">
              At Jarthaz Tours, we believe every journey is a story waiting to be told. Let's craft yours together.
            </p>
            <Button size="lg" variant="safari" asChild className="btn-shimmer">
              <Link href="/contact">Contact Us Now</Link>
            </Button>
          </AnimatedSection>
        </div>
      </section>
    </div>
  )
}
