import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Search, Filter, Edit, Trash2, UserPlus, Shield, User, MoreHorizontal } from "lucide-react"
import Link from "next/link"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"

// Mock data for users
const users = [
  {
    id: "user-001",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "admin",
    status: "active",
    lastLogin: "2023-05-12 09:45 AM",
    avatar: "",
  },
  {
    id: "user-002",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "editor",
    status: "active",
    lastLogin: "2023-05-11 02:30 PM",
    avatar: "",
  },
  {
    id: "user-003",
    name: "Michael Brown",
    email: "<EMAIL>",
    role: "viewer",
    status: "inactive",
    lastLogin: "2023-04-28 11:20 AM",
    avatar: "",
  },
  {
    id: "user-004",
    name: "Emily Davis",
    email: "<EMAIL>",
    role: "editor",
    status: "active",
    lastLogin: "2023-05-10 04:15 PM",
    avatar: "",
  },
  {
    id: "user-005",
    name: "David Wilson",
    email: "<EMAIL>",
    role: "admin",
    status: "active",
    lastLogin: "2023-05-12 08:30 AM",
    avatar: "",
  },
]

const getRoleBadge = (role: string) => {
  switch (role) {
    case "admin":
      return (
        <Badge variant="outline" className="bg-purple-50 text-purple-700 hover:bg-purple-50 border-purple-200">
          <Shield className="h-3 w-3 mr-1" /> Admin
        </Badge>
      )
    case "editor":
      return (
        <Badge variant="outline" className="bg-blue-50 text-blue-700 hover:bg-blue-50 border-blue-200">
          <Edit className="h-3 w-3 mr-1" /> Editor
        </Badge>
      )
    case "viewer":
      return (
        <Badge variant="outline" className="bg-gray-50 text-gray-700 hover:bg-gray-50 border-gray-200">
          <User className="h-3 w-3 mr-1" /> Viewer
        </Badge>
      )
    default:
      return <Badge variant="outline">Unknown</Badge>
  }
}

const getStatusBadge = (status: string) => {
  switch (status) {
    case "active":
      return (
        <Badge variant="outline" className="bg-green-50 text-green-700 hover:bg-green-50 border-green-200">
          Active
        </Badge>
      )
    case "inactive":
      return (
        <Badge variant="outline" className="bg-amber-50 text-amber-700 hover:bg-amber-50 border-amber-200">
          Inactive
        </Badge>
      )
    default:
      return <Badge variant="outline">Unknown</Badge>
  }
}

export default function UsersPage() {
  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">User Management</h2>
        <Button asChild>
          <Link href="/admin/users/new">
            <UserPlus className="mr-2 h-4 w-4" /> Add User
          </Link>
        </Button>
      </div>

      <div className="flex items-center gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input type="search" placeholder="Search users..." className="pl-8" />
        </div>
        <Button variant="outline">
          <Filter className="mr-2 h-4 w-4" /> Filter
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>System Users</CardTitle>
          <CardDescription>Manage user accounts and permissions for the admin dashboard.</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>User</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Last Login</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {users.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Avatar>
                        <AvatarImage src={user.avatar || "/placeholder.svg"} alt={user.name} />
                        <AvatarFallback>
                          {user.name
                            .split(" ")
                            .map((n) => n[0])
                            .join("")}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{user.name}</div>
                        <div className="text-sm text-muted-foreground">{user.email}</div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>{getRoleBadge(user.role)}</TableCell>
                  <TableCell>{getStatusBadge(user.status)}</TableCell>
                  <TableCell>{user.lastLogin}</TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Open menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>
                          <Edit className="mr-2 h-4 w-4" /> Edit User
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Shield className="mr-2 h-4 w-4" /> Change Role
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Trash2 className="mr-2 h-4 w-4" /> Delete User
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>User Permissions</CardTitle>
          <CardDescription>Configure access levels for different user roles.</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-4">Admin Role</h3>
              <div className="grid gap-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="admin-full-access">Full System Access</Label>
                    <p className="text-sm text-muted-foreground">Can access and modify all system settings</p>
                  </div>
                  <Switch id="admin-full-access" defaultChecked />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="admin-user-management">User Management</Label>
                    <p className="text-sm text-muted-foreground">Can create, edit, and delete user accounts</p>
                  </div>
                  <Switch id="admin-user-management" defaultChecked />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="admin-settings">System Settings</Label>
                    <p className="text-sm text-muted-foreground">Can modify global system settings</p>
                  </div>
                  <Switch id="admin-settings" defaultChecked />
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-4">Editor Role</h3>
              <div className="grid gap-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="editor-content">Content Management</Label>
                    <p className="text-sm text-muted-foreground">Can create and edit website content</p>
                  </div>
                  <Switch id="editor-content" defaultChecked />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="editor-media">Media Library</Label>
                    <p className="text-sm text-muted-foreground">Can upload and manage media files</p>
                  </div>
                  <Switch id="editor-media" defaultChecked />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="editor-inquiries">Inquiries</Label>
                    <p className="text-sm text-muted-foreground">Can view and respond to customer inquiries</p>
                  </div>
                  <Switch id="editor-inquiries" defaultChecked />
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-4">Viewer Role</h3>
              <div className="grid gap-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="viewer-dashboard">Dashboard</Label>
                    <p className="text-sm text-muted-foreground">Can view dashboard statistics</p>
                  </div>
                  <Switch id="viewer-dashboard" defaultChecked />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="viewer-content">Content</Label>
                    <p className="text-sm text-muted-foreground">Can view but not edit website content</p>
                  </div>
                  <Switch id="viewer-content" defaultChecked />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="viewer-inquiries">Inquiries</Label>
                    <p className="text-sm text-muted-foreground">Can view but not respond to inquiries</p>
                  </div>
                  <Switch id="viewer-inquiries" defaultChecked />
                </div>
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          <Button>Save Permission Settings</Button>
        </CardFooter>
      </Card>
    </div>
  )
}
