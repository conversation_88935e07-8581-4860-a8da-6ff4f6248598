@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 10%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 10%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 10%;
    --primary: 42 95% 55%;
    --primary-foreground: 0 0% 0%;
    --secondary: 0 0% 96%;
    --secondary-foreground: 0 0% 10%;
    --muted: 0 0% 96%;
    --muted-foreground: 0 0% 45%;
    --accent: 42 95% 55%;
    --accent-foreground: 0 0% 10%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 90%;
    --input: 0 0% 90%;
    --ring: 42 95% 55%;
    --radius: 0.5rem;
    --jarthaz-gold: 42 95% 55%;
  }

  .dark {
    --background: 0 0% 10%;
    --foreground: 0 0% 98%;
    --card: 0 0% 15%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 15%;
    --popover-foreground: 0 0% 98%;
    --primary: 42 95% 55%;
    --primary-foreground: 0 0% 0%;
    --secondary: 0 0% 20%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 20%;
    --muted-foreground: 0 0% 70%;
    --accent: 42 95% 55%;
    --accent-foreground: 0 0% 10%;
    --destructive: 0 62% 30%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 20%;
    --input: 0 0% 20%;
    --ring: 42 95% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  .hero-overlay {
    @apply absolute inset-0 bg-gradient-to-t from-jarthaz-black/80 via-jarthaz-black/40 to-transparent;
  }

  /* Safari pattern background */
  .safari-pattern-bg {
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23d4af37' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  }

  /* Card hover effect */
  .card-hover {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .card-hover:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  .btn-safari {
    @apply relative overflow-hidden transition-all duration-300 hover:shadow-md;
  }

  .btn-safari::after {
    content: "";
    @apply absolute inset-0 bg-white/20 scale-x-0 origin-left transition-transform duration-300;
  }

  .btn-safari:hover::after {
    @apply scale-x-100;
  }

  .section-heading {
    @apply text-3xl font-bold tracking-tight mb-4 relative inline-block;
  }

  .section-heading::after {
    content: "";
    @apply absolute bottom-0 left-0 w-1/3 h-1 bg-jarthaz-gold rounded;
  }

  /* Safari divider */
  .safari-divider {
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.5), transparent);
    margin: 0 auto;
  }

  .image-hover-zoom {
    @apply overflow-hidden;
  }

  .image-hover-zoom img {
    @apply transition-transform duration-500 ease-in-out;
  }

  .image-hover-zoom:hover img {
    @apply scale-110;
  }

  /* Text shadow for better contrast on hero sections */
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  }

  .safari-silhouette {
    position: relative;
  }

  .safari-silhouette::before {
    content: "";
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 50px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%23000000' fill-opacity='0.5' d='M0,224L48,213.3C96,203,192,181,288,181.3C384,181,480,203,576,202.7C672,203,768,181,864,181.3C960,181,1056,203,1152,208C1248,213,1344,203,1392,197.3L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z'%3E%3C/path%3E%3C/svg%3E");
    background-size: cover;
    background-position: bottom;
    z-index: 1;
  }

  .safari-silhouette-icon {
    width: 60px;
    height: 40px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 640 512' fill='%23F9B81F' opacity='0.7'%3E%3Cpath d='M171.3 96H224v96H111.3l30.4-75.9C146.5 104 158.2 96 171.3 96zM272 192V96h81.2c9.7 0 18.9 4.4 25 12l67.2 84H272zm256.2 1L428.2 68c-18.2-22.8-45.8-36-75-36H171.3c-39.3 0-74.6 23.9-89.1 60.3L40.6 196.4C16.8 205.8 0 228.9 0 256v112c0 17.7 14.3 32 32 32h33.3c7.6 45.4 47.1 80 94.7 80s87.1-34.6 94.7-80H130.6c7.6 45.4 47.1 80 94.7 80s87.1-34.6 94.7-80H608c17.7 0 32-14.3 32-32V320c0-65.2-48.8-119-111.8-127zm-2.9 207c-6.6 18.6-24.4 32-45.3 32s-38.7-13.4-45.3-32c-1.8-5-2.7-10.4-2.7-16c0-26.5 21.5-48 48-48s48 21.5 48 48c0 5.6-1 11-2.7 16zM160 432c-20.9 0-38.7-13.4-45.3-32c-1.8-5-2.7-10.4-2.7-16c0-26.5 21.5-48 48-48s48 21.5 48 48c0 5.6-1 11-2.7 16c-6.6 18.6-24.4 32-45.3 32z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #f9b81f;
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background: #d99a00;
}

/* Safari vehicle animation */
.safari-vehicle {
  position: absolute;
  bottom: 20px;
  left: -100px;
  width: 80px;
  height: 40px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 50' fill='%23000000'%3E%3Cpath d='M90,30H75V20H60V30H10C4.5,30,0,34.5,0,40v5h100v-5C100,34.5,95.5,30,90,30z M20,40c-2.8,0-5-2.2-5-5s2.2-5,5-5s5,2.2,5,5S22.8,40,20,40z M80,40c-2.8,0-5-2.2-5-5s2.2-5,5-5s5,2.2,5,5S82.8,40,80,40z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  animation: safari-drive 20s linear infinite;
  z-index: 2;
}

/* Animated underline for navigation */
.nav-link {
  position: relative;
}

.nav-link::after {
  content: "";
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -2px;
  left: 0;
  background-color: #f9b81f;
  transition: width 0.3s ease;
}

.nav-link:hover::after {
  width: 100%;
}

/* Animated destination cards */
.destination-card {
  transition: all 0.3s ease;
  overflow: hidden;
}

.destination-card:hover {
  transform: translateY(-10px);
}

.destination-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0) 50%);
  z-index: 1;
}

.destination-card img {
  transition: transform 0.5s ease;
}

.destination-card:hover img {
  transform: scale(1.1);
}

/* Animated CTA button */
.cta-button {
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.cta-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.7s ease;
  z-index: -1;
}

.cta-button:hover::before {
  left: 100%;
}

/* Parallax effect */
.parallax {
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

/* Floating animation for icons */
.float-icon {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

/* Safari drive animation */
@keyframes safari-drive {
  0% {
    transform: translateX(-100px);
  }
  100% {
    transform: translateX(calc(100vw + 100px));
  }
}

/* Animal tracks animation */
.animal-tracks {
  position: relative;
}

.animal-tracks::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23F9B81F' fill-opacity='0.2'%3E%3Cpath d='M10 10c0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6h2c0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4v2c-3.314 0-6-2.686-6-6 0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6zm25.464-1.95l8.486 8.486-1.414 1.414-8.486-8.486 1.414-1.414z' /%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  animation: animalTracks 15s linear infinite;
}

@keyframes animalTracks {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 100% 100%;
  }
}

/* Button shimmer effect */
.btn-shimmer {
  position: relative;
  overflow: hidden;
}

.btn-shimmer::after {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(30deg);
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) rotate(30deg);
  }
  100% {
    transform: translateX(100%) rotate(30deg);
  }
}
