"use client"

import Image from "next/image"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import AnimatedSection from "@/components/animated-section"
import { motion } from "framer-motion"
import { Shield, Eye, Cookie, Database, Users, Lock, FileText, ArrowLeft } from "lucide-react"

export default function PrivacyPolicyPage() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative h-[50vh] flex items-center overflow-hidden">
        <div className="absolute inset-0 z-0">
          <Image
            src="/images/wildlife/queen-elizabeth-landscape.jpeg"
            alt="Beautiful landscape representing trust and transparency"
            fill
            className="object-cover brightness-50"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-b from-black/40 via-transparent to-black/60"></div>
        </div>

        <div className="container relative z-10 text-white">
          <motion.div
            className="max-w-2xl space-y-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <div className="inline-block bg-jarthaz-gold/20 backdrop-blur-sm px-4 py-1 rounded-full text-white text-sm font-medium border border-jarthaz-gold/30">
              <Shield size={14} className="inline mr-2" />
              Your Privacy Matters
            </div>
            <h1 className="text-4xl font-bold tracking-tighter sm:text-5xl md:text-6xl text-shadow">
              Privacy <span className="text-jarthaz-gold">Policy</span>
            </h1>
            <p className="text-lg text-white/90 max-w-xl">
              Learn how we protect and handle your personal information with transparency and care.
            </p>
            <Button variant="outline" size="sm" asChild className="bg-white/10 backdrop-blur-sm border-white/20 text-white hover:bg-white/20">
              <Link href="/">
                <ArrowLeft size={16} className="mr-2" />
                Back to Home
              </Link>
            </Button>
          </motion.div>
        </div>

        {/* Wave divider at bottom */}
        <div className="absolute bottom-0 left-0 right-0">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320" className="text-background fill-current">
            <path d="M0,224L48,213.3C96,203,192,181,288,181.3C384,181,480,203,576,202.7C672,203,768,181,864,181.3C960,181,1056,203,1152,208C1248,213,1344,203,1392,197.3L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path>
          </svg>
        </div>
      </section>

      {/* Privacy Policy Content */}
      <section className="py-20">
        <div className="container max-w-4xl">
          <AnimatedSection>
            <div className="prose prose-lg max-w-none dark:prose-invert">
              <div className="mb-8 p-6 bg-jarthaz-gold/10 rounded-lg border border-jarthaz-gold/20">
                <p className="text-sm text-muted-foreground mb-2">Last updated: {new Date().toLocaleDateString()}</p>
                <p className="text-base">
                  At Jarthaz Tours, we are committed to protecting your privacy and ensuring the security of your personal information.
                  This Privacy Policy explains how we collect, use, and safeguard your data when you visit our website at{" "}
                  <Link href="https://tours.jarthazgroup.com" className="text-jarthaz-gold hover:underline">
                    https://tours.jarthazgroup.com
                  </Link>
                </p>
              </div>

              {/* Who We Are */}
              <AnimatedSection delay={0.2}>
                <div className="mb-12">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="bg-jarthaz-gold/10 p-2 rounded-lg">
                      <Users className="h-6 w-6 text-jarthaz-gold" />
                    </div>
                    <h2 className="text-2xl font-bold">Who We Are</h2>
                  </div>
                  <p className="text-muted-foreground">
                    Our website address is: <Link href="https://tours.jarthazgroup.com" className="text-jarthaz-gold hover:underline">https://tours.jarthazgroup.com</Link>
                  </p>
                </div>
              </AnimatedSection>

              {/* Comments */}
              <AnimatedSection delay={0.3}>
                <div className="mb-12">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="bg-jarthaz-gold/10 p-2 rounded-lg">
                      <FileText className="h-6 w-6 text-jarthaz-gold" />
                    </div>
                    <h2 className="text-2xl font-bold">Comments</h2>
                  </div>
                  <div className="space-y-4 text-muted-foreground">
                    <p>
                      When visitors leave comments on the site we collect the data shown in the comments form, and also the visitor's IP address and browser user agent string to help spam detection.
                    </p>
                    <p>
                      An anonymized string created from your email address (also called a hash) may be provided to the Gravatar service to see if you are using it. After approval of your comment, your profile picture is visible to the public in the context of your comment.
                    </p>
                  </div>
                </div>
              </AnimatedSection>

              {/* Media */}
              <AnimatedSection delay={0.4}>
                <div className="mb-12">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="bg-jarthaz-gold/10 p-2 rounded-lg">
                      <Eye className="h-6 w-6 text-jarthaz-gold" />
                    </div>
                    <h2 className="text-2xl font-bold">Media</h2>
                  </div>
                  <p className="text-muted-foreground">
                    If you upload images to the website, you should avoid uploading images with embedded location data (EXIF GPS) included. Visitors to the website can download and extract any location data from images on the website.
                  </p>
                </div>
              </AnimatedSection>

              {/* Cookies */}
              <AnimatedSection delay={0.5}>
                <div className="mb-12">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="bg-jarthaz-gold/10 p-2 rounded-lg">
                      <Cookie className="h-6 w-6 text-jarthaz-gold" />
                    </div>
                    <h2 className="text-2xl font-bold">Cookies</h2>
                  </div>
                  <div className="space-y-4 text-muted-foreground">
                    <p>
                      If you leave a comment on our site you may opt-in to saving your name, email address and website in cookies. These are for your convenience so that you do not have to fill in your details again when you leave another comment. These cookies will last for one year.
                    </p>
                    <p>
                      If you visit our login page, we will set a temporary cookie to determine if your browser accepts cookies. This cookie contains no personal data and is discarded when you close your browser.
                    </p>
                    <p>
                      When you log in, we will also set up several cookies to save your login information and your screen display choices. Login cookies last for two days, and screen options cookies last for a year. If you select "Remember Me", your login will persist for two weeks. If you log out of your account, the login cookies will be removed.
                    </p>
                    <p>
                      If you edit or publish an article, an additional cookie will be saved in your browser. This cookie includes no personal data and simply indicates the post ID of the article you just edited. It expires after 1 day.
                    </p>
                  </div>
                </div>
              </AnimatedSection>

              {/* Embedded Content */}
              <AnimatedSection delay={0.6}>
                <div className="mb-12">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="bg-jarthaz-gold/10 p-2 rounded-lg">
                      <Database className="h-6 w-6 text-jarthaz-gold" />
                    </div>
                    <h2 className="text-2xl font-bold">Embedded Content from Other Websites</h2>
                  </div>
                  <div className="space-y-4 text-muted-foreground">
                    <p>
                      Articles on this site may include embedded content (e.g. videos, images, articles, etc.). Embedded content from other websites behaves in the exact same way as if the visitor has visited the other website.
                    </p>
                    <p>
                      These websites may collect data about you, use cookies, embed additional third-party tracking, and monitor your interaction with that embedded content, including tracking your interaction with the embedded content if you have an account and are logged in to that website.
                    </p>
                  </div>
                </div>
              </AnimatedSection>

              {/* Data Sharing */}
              <AnimatedSection delay={0.7}>
                <div className="mb-12">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="bg-jarthaz-gold/10 p-2 rounded-lg">
                      <Users className="h-6 w-6 text-jarthaz-gold" />
                    </div>
                    <h2 className="text-2xl font-bold">Who We Share Your Data With</h2>
                  </div>
                  <p className="text-muted-foreground">
                    If you request a password reset, your IP address will be included in the reset email.
                  </p>
                </div>
              </AnimatedSection>

              {/* Data Retention */}
              <AnimatedSection delay={0.8}>
                <div className="mb-12">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="bg-jarthaz-gold/10 p-2 rounded-lg">
                      <Lock className="h-6 w-6 text-jarthaz-gold" />
                    </div>
                    <h2 className="text-2xl font-bold">How Long We Retain Your Data</h2>
                  </div>
                  <div className="space-y-4 text-muted-foreground">
                    <p>
                      If you leave a comment, the comment and its metadata are retained indefinitely. This is so we can recognize and approve any follow-up comments automatically instead of holding them in a moderation queue.
                    </p>
                    <p>
                      For users that register on our website (if any), we also store the personal information they provide in their user profile. All users can see, edit, or delete their personal information at any time (except they cannot change their username). Website administrators can also see and edit that information.
                    </p>
                  </div>
                </div>
              </AnimatedSection>

              {/* Your Rights */}
              <AnimatedSection delay={0.9}>
                <div className="mb-12">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="bg-jarthaz-gold/10 p-2 rounded-lg">
                      <Shield className="h-6 w-6 text-jarthaz-gold" />
                    </div>
                    <h2 className="text-2xl font-bold">What Rights You Have Over Your Data</h2>
                  </div>
                  <p className="text-muted-foreground">
                    If you have an account on this site, or have left comments, you can request to receive an exported file of the personal data we hold about you, including any data you have provided to us. You can also request that we erase any personal data we hold about you. This does not include any data we are obliged to keep for administrative, legal, or security purposes.
                  </p>
                </div>
              </AnimatedSection>

              {/* Data Transmission */}
              <AnimatedSection delay={1.0}>
                <div className="mb-12">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="bg-jarthaz-gold/10 p-2 rounded-lg">
                      <Database className="h-6 w-6 text-jarthaz-gold" />
                    </div>
                    <h2 className="text-2xl font-bold">Where Your Data Is Sent</h2>
                  </div>
                  <p className="text-muted-foreground">
                    Visitor comments may be checked through an automated spam detection service.
                  </p>
                </div>
              </AnimatedSection>

              {/* Contact Information */}
              <AnimatedSection delay={1.1}>
                <div className="mb-12 p-6 bg-muted/50 rounded-lg border">
                  <h2 className="text-2xl font-bold mb-4">Contact Us</h2>
                  <p className="text-muted-foreground mb-4">
                    If you have any questions about this Privacy Policy or how we handle your personal information, please contact us:
                  </p>
                  <div className="space-y-2 text-sm">
                    <p><strong>Email:</strong> <Link href="mailto:<EMAIL>" className="text-jarthaz-gold hover:underline"><EMAIL></Link></p>
                    <p><strong>Phone:</strong> +256-788-359-512</p>
                    <p><strong>Address:</strong> Kataza, Kampala, Uganda</p>
                  </div>
                </div>
              </AnimatedSection>
            </div>
          </AnimatedSection>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-jarthaz-black text-white relative overflow-hidden">
        <div className="absolute inset-0 z-0">
          <Image
            src="/images/wildlife/murchison-falls-aerial.jpeg"
            alt="Uganda landscape"
            fill
            className="object-cover opacity-20"
          />
        </div>

        <div className="container text-center relative z-10">
          <AnimatedSection>
            <h2 className="text-3xl font-bold tracking-tight mb-4 text-jarthaz-gold">
              Ready to Explore Uganda?
            </h2>
            <p className="mb-8 max-w-2xl mx-auto text-white/90">
              Now that you understand how we protect your privacy, let's start planning your unforgettable Ugandan adventure.
            </p>
            <div className="flex flex-wrap gap-4 justify-center">
              <Button size="lg" variant="safari" asChild className="btn-shimmer">
                <Link href="/contact">Contact Us</Link>
              </Button>
              <Button size="lg" variant="outline" asChild className="bg-white/10 backdrop-blur-sm border-white/20 text-white hover:bg-white/20">
                <Link href="/destinations">View Destinations</Link>
              </Button>
            </div>
          </AnimatedSection>
        </div>
      </section>
    </div>
  )
}
