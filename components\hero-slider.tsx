"use client"

import { useState, useEffect, useCallback } from "react"
import Image from "next/image"
import Link from "next/link"
import { motion, AnimatePresence } from "framer-motion"
import { Button } from "@/components/ui/button"
import { ChevronLeft, ChevronRight, ArrowRight } from "lucide-react"

// Define the slide type
interface Slide {
  id: number
  title: string
  subtitle: string
  description: string
  cta: {
    primary: {
      text: string
      link: string
    }
    secondary?: {
      text: string
      link: string
    }
  }
  image: string
  alt: string
  position?: "center" | "left" | "right"
}

// Sample slides data - replace images with your actual images
const slides: Slide[] = [
  {
    id: 1,
    title: "Discover Uganda's Wildlife",
    subtitle: "The Pearl of Africa",
    description: "Experience breathtaking safaris and encounter majestic wildlife in their natural habitat.",
    cta: {
      primary: {
        text: "Explore Safaris",
        link: "/packages/wildlife-safaris",
      },
      secondary: {
        text: "Learn More",
        link: "/about",
      },
    },
    image: "/images/wildlife/queen-elizabeth-landscape.jpeg",
    alt: "Uganda wildlife safari with elephants and giraffes",
    position: "center",
  },
  {
    id: 2,
    title: "Gorilla Trekking Adventures",
    subtitle: "Once-in-a-lifetime Experience",
    description: "Come face to face with mountain gorillas in their natural habitat in Bwindi Impenetrable Forest.",
    cta: {
      primary: {
        text: "Book Trekking",
        link: "/packages/gorilla-trekking",
      },
      secondary: {
        text: "View Packages",
        link: "/destinations",
      },
    },
    image: "/images/gorillas/gorilla-family.jpeg",
    alt: "Mountain gorilla in Bwindi Impenetrable Forest",
    position: "right",
  },
  {
    id: 3,
    title: "Cultural Immersion",
    subtitle: "Connect with Local Communities",
    description: "Immerse yourself in Uganda's rich cultural heritage and connect with welcoming local communities.",
    cta: {
      primary: {
        text: "Cultural Tours",
        link: "/packages/cultural-tours",
      },
      secondary: {
        text: "Contact Us",
        link: "/contact",
      },
    },
    image: "/images/cultural-tours/Ndere-1.jpg",
    alt: "Traditional Ugandan cultural dance performance",
    position: "left",
  },
  {
    id: 4,
    title: "Adventure Awaits",
    subtitle: "Thrilling Experiences",
    description: "From white water rafting on the Nile to hiking volcanic mountains, adventure awaits in Uganda.",
    cta: {
      primary: {
        text: "Adventure Tours",
        link: "/packages/adventure-escapades",
      },
    },
    image: "/images/adventures/rafting-nile.jpeg",
    alt: "White water rafting on the Nile River in Uganda",
    position: "center",
  },
]

export default function HeroSlider() {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)
  const [direction, setDirection] = useState(0) // -1 for left, 1 for right
  const [isAnimating, setIsAnimating] = useState(false)

  // Auto-advance slides
  useEffect(() => {
    if (!isAutoPlaying) return

    const interval = setInterval(() => {
      if (!isAnimating) {
        setDirection(1)
        setCurrentSlide((prev) => (prev + 1) % slides.length)
      }
    }, 6000)

    return () => clearInterval(interval)
  }, [isAutoPlaying, isAnimating])

  // Pause auto-play on hover
  const pauseAutoPlay = () => setIsAutoPlaying(false)
  const resumeAutoPlay = () => setIsAutoPlaying(true)

  // Navigation handlers
  const goToSlide = useCallback(
    (index: number) => {
      if (isAnimating) return
      setDirection(index > currentSlide ? 1 : -1)
      setCurrentSlide(index)
    },
    [currentSlide, isAnimating],
  )

  const nextSlide = useCallback(() => {
    if (isAnimating) return
    setDirection(1)
    setCurrentSlide((prev) => (prev + 1) % slides.length)
  }, [isAnimating])

  const prevSlide = useCallback(() => {
    if (isAnimating) return
    setDirection(-1)
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length)
  }, [isAnimating])

  // Variants for animations
  // Preload all slide images to prevent white flash during transitions
  useEffect(() => {
    slides.forEach(slide => {
      const img = new window.Image();
      img.src = slide.image;
    });
  }, []);
  
  // Variants for animations with improved cross-fade effect
  const slideVariants = {
    enter: (direction: number) => ({
      x: direction > 0 ? "30%" : "-30%",
      opacity: 0,
      scale: 1.05,
      zIndex: 1,
    }),
    center: {
      x: 0,
      opacity: 1,
      scale: 1,
      zIndex: 2,
      transition: {
        x: { type: "spring", stiffness: 300, damping: 30 },
        opacity: { duration: 0.8 },
        scale: { duration: 0.8 },
      },
    },
    exit: (direction: number) => ({
      x: direction > 0 ? "-30%" : "30%",
      opacity: 0,
      scale: 0.95,
      zIndex: 0,
      transition: {
        x: { type: "spring", stiffness: 300, damping: 30 },
        opacity: { duration: 0.8 },
      },
    }),
  }

  const textVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: (custom: number) => ({
      opacity: 1,
      y: 0,
      transition: { delay: custom * 0.15, duration: 0.8, ease: "easeOut" },
    }),
    exit: { opacity: 0, y: -20, transition: { duration: 0.3 } },
  }

  // Get content position class based on slide position preference
  const getContentPositionClass = (position: Slide["position"]) => {
    switch (position) {
      case "left":
        return "items-start text-left"
      case "right":
        return "items-end text-right"
      default:
        return "items-center text-center"
    }
  }

  // Get previous slide for background layer
  const prevSlideIndex = (currentSlide - 1 + slides.length) % slides.length;
  const nextSlideIndex = (currentSlide + 1) % slides.length;
  
  return (
    <div className="relative h-[90vh] overflow-hidden" onMouseEnter={pauseAutoPlay} onMouseLeave={resumeAutoPlay}>
      {/* Background layer with previous slide to prevent white flashes */}
      <div className="absolute inset-0 z-0">
        <Image
          src={slides[prevSlideIndex].image}
          alt="Background layer"
          fill
          className="object-cover"
          priority
        />
        <div className="absolute inset-0 bg-gradient-to-r from-jarthaz-black/70 to-jarthaz-black/30"></div>
      </div>
      {/* Slides */}
      <AnimatePresence initial={false} custom={direction} mode="sync" onExitComplete={() => setIsAnimating(false)}>
        <motion.div
          key={currentSlide}
          custom={direction}
          variants={slideVariants}
          initial="enter"
          animate="center"
          exit="exit"
          onAnimationStart={() => setIsAnimating(true)}
          onAnimationComplete={() => setIsAnimating(false)}
          className="absolute inset-0"
        >
          {/* Background Image with Zoom Effect */}
          <motion.div
            className="absolute inset-0 z-10"
            initial={{ scale: 1 }}
            animate={{ scale: 1.05 }}
            transition={{ duration: 6, ease: "linear" }}
          >
            <Image
              src={slides[currentSlide].image || "/placeholder.svg"}
              alt={slides[currentSlide].alt}
              fill
              className="object-cover"
              priority
            />
            <div className="absolute inset-0 bg-gradient-to-r from-jarthaz-black/70 to-jarthaz-black/30"></div>
          </motion.div>

          {/* Safari vehicle silhouette */}
          <div className="safari-vehicle"></div>

          {/* Content */}
          <div
            className={`container relative z-20 h-full flex ${getContentPositionClass(
              slides[currentSlide].position,
            )} text-white`}
          >
            <div className="max-w-2xl space-y-5 p-6 rounded-lg backdrop-blur-sm bg-jarthaz-black/20 border border-white/10">
              <motion.span
                custom={0}
                variants={textVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
                className="inline-block px-3 py-1 bg-jarthaz-gold text-jarthaz-black text-sm font-medium rounded-full mb-2"
              >
                {slides[currentSlide].subtitle}
              </motion.span>

              <motion.h1
                custom={1}
                variants={textVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
                className="text-4xl font-bold tracking-tighter sm:text-5xl md:text-6xl text-shadow"
              >
                {slides[currentSlide].title}
              </motion.h1>

              <motion.p
                custom={2}
                variants={textVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
                className="text-lg md:text-xl text-white/90"
              >
                {slides[currentSlide].description}
              </motion.p>

              <motion.div
                custom={3}
                variants={textVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
                className="flex flex-col sm:flex-row gap-4 pt-2"
              >
                <Button size="lg" variant="safari" asChild className="cta-button group">
                  <Link href={slides[currentSlide].cta.primary.link}>
                    {slides[currentSlide].cta.primary.text}
                    <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </Link>
                </Button>

                {slides[currentSlide].cta.secondary && (
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent text-white border-white hover:bg-white hover:text-jarthaz-black"
                    asChild
                  >
                    <Link href={slides[currentSlide].cta.secondary.link}>
                      {slides[currentSlide].cta.secondary.text}
                    </Link>
                  </Button>
                )}
              </motion.div>
            </div>
          </div>

          {/* Decorative elements */}
          <div className="absolute bottom-20 left-10 w-32 h-32 border-l-2 border-b-2 border-jarthaz-gold/30 rounded-bl-3xl" />
          <div className="absolute top-20 right-10 w-32 h-32 border-t-2 border-r-2 border-jarthaz-gold/30 rounded-tr-3xl" />
        </motion.div>
      </AnimatePresence>

      {/* Navigation Arrows */}
      <button
        onClick={prevSlide}
        className="absolute left-4 top-1/2 -translate-y-1/2 z-20 bg-black/30 hover:bg-black/50 text-white p-3 rounded-full transition-all transform hover:scale-110"
        aria-label="Previous slide"
      >
        <ChevronLeft size={24} />
      </button>

      <button
        onClick={nextSlide}
        className="absolute right-4 top-1/2 -translate-y-1/2 z-20 bg-black/30 hover:bg-black/50 text-white p-3 rounded-full transition-all transform hover:scale-110"
        aria-label="Next slide"
      >
        <ChevronRight size={24} />
      </button>

      {/* Slide Indicators */}
      <div className="absolute bottom-8 left-1/2 -translate-x-1/2 z-20 flex space-x-2">
        {slides.map((_, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={`w-3 h-3 rounded-full transition-all ${
              index === currentSlide ? "bg-jarthaz-gold w-10 h-3" : "bg-white/50 hover:bg-white/80 hover:scale-110"
            }`}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>

      {/* Wave Overlay */}
      <div className="absolute bottom-0 left-0 right-0 z-10">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320" className="w-full">
          <path
            fill="#ffffff"
            fillOpacity="1"
            d="M0,224L48,213.3C96,203,192,181,288,181.3C384,181,480,203,576,202.7C672,203,768,181,864,181.3C960,181,1056,203,1152,208C1248,213,1344,203,1392,197.3L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"
          ></path>
        </svg>
      </div>
    </div>
  )
}
