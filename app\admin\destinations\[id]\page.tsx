import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { ArrowLeft, Save, Trash2 } from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

// This would normally fetch data based on the ID
export default function EditDestinationPage({ params }: { params: { id: string } }) {
  const isNew = params.id === "new"

  // Mock data for an existing destination
  const destination = isNew
    ? {
        id: "",
        name: "",
        type: "",
        image: "/placeholder.svg?height=400&width=600",
        description: "",
        activities: [],
      }
    : {
        id: "bwindi",
        name: "Bwindi Impenetrable National Park",
        type: "National Park",
        image: "/placeholder.svg?height=400&width=600",
        description:
          "Home to half of the world's mountain gorilla population, this UNESCO World Heritage Site promises a magical encounter with nature.",
        activities: ["Gorilla trekking", "Forest hikes", "Bird watching", "Community visits"],
      }

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" asChild>
            <Link href="/admin/destinations">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h2 className="text-3xl font-bold tracking-tight">
            {isNew ? "Add New Destination" : `Edit ${destination.name}`}
          </h2>
        </div>
        <div className="flex items-center gap-2">
          {!isNew && (
            <Button variant="destructive">
              <Trash2 className="mr-2 h-4 w-4" /> Delete
            </Button>
          )}
          <Button>
            <Save className="mr-2 h-4 w-4" /> Save
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-[2fr_1fr]">
        <div className="space-y-6">
          <Card>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div className="grid gap-2">
                  <Label htmlFor="name">Destination Name</Label>
                  <Input id="name" defaultValue={destination.name} placeholder="Enter destination name" />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="type">Type</Label>
                  <Select defaultValue={destination.type}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select destination type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="National Park">National Park</SelectItem>
                      <SelectItem value="Lake">Lake</SelectItem>
                      <SelectItem value="Adventure Hub">Adventure Hub</SelectItem>
                      <SelectItem value="Cultural Site">Cultural Site</SelectItem>
                      <SelectItem value="City">City</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    defaultValue={destination.description}
                    placeholder="Enter destination description"
                    rows={5}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label>Activities</Label>
                  <Button variant="outline" size="sm">
                    Add Activity
                  </Button>
                </div>
                <div className="space-y-2">
                  {destination.activities.map((activity, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <Input defaultValue={activity} className="flex-1" />
                      <Button variant="ghost" size="icon">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                  {destination.activities.length === 0 && (
                    <p className="text-sm text-muted-foreground">No activities added yet.</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="space-y-6">
          <Card>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <Label>Featured Image</Label>
                <div className="relative aspect-video overflow-hidden rounded-md border">
                  <Image
                    src={destination.image || "/placeholder.svg"}
                    alt="Featured image"
                    fill
                    className="object-cover"
                  />
                </div>
                <Button variant="outline" className="w-full">
                  Change Image
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <Label>SEO Settings</Label>
                <div className="grid gap-2">
                  <Label htmlFor="seo-title" className="text-sm">
                    SEO Title
                  </Label>
                  <Input id="seo-title" placeholder="SEO title" />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="seo-description" className="text-sm">
                    Meta Description
                  </Label>
                  <Textarea id="seo-description" placeholder="Meta description" rows={3} />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
