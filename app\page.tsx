"use client"

import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ChevronRight, Mountain, Users, Compass, Camera, Heart, Bird, ArrowRight } from "lucide-react"
import AnimatedSection from "@/components/animated-section"
import SafariIcon from "@/components/safari-icon"
import DestinationCard from "@/components/destination-card"
import PackageCard from "@/components/package-card"
import HeroSlider from "@/components/hero-slider"
import SafariDivider from "@/components/safari-divider"
import StatsSection from "@/components/stats-section"
import ParallaxSection from "@/components/parallax-section"
import { motion } from "framer-motion"

export default function Home() {
  // Tour packages data
  const tourPackages = [
    {
      id: "gorilla-trekking",
      title: "Gorilla Trekking Adventures",
      description: "A once-in-a-lifetime journey to meet Uganda's gentle giants in their natural habitat.",
      image: "/images/gorillas/gorilla-closeup.jpeg",
      alt: "Gorilla Trekking",
      duration: "3-5 days",
      groupSize: "4-8",
      startingPrice: "$1,500",
    },
    {
      id: "wildlife-safaris",
      title: "Wildlife Safaris",
      description: "Experience the thrill of Uganda's wildlife in its natural splendor.",
      image: "/images/wildlife/elephant-family-savanna.jpeg",
      alt: "Wildlife Safari",
      duration: "5-10 days",
      groupSize: "2-6",
      startingPrice: "$1,200",
    },
    {
      id: "adventure-escapades",
      title: "Adventure Escapades",
      description: "For adrenaline seekers, Uganda offers endless excitement.",
      image: "/images/adventures/bungee-jumping.jpeg",
      alt: "Adventure Activities",
      duration: "4-7 days",
      groupSize: "2-10",
      startingPrice: "$950",
    },
  ]

  // Destinations data
  const destinations = [
    {
      name: "Bwindi Impenetrable",
      type: "National Park",
      image: "/images/gorillas/gorilla-family.jpeg",
      alt: "Bwindi Impenetrable National Park",
    },
    {
      name: "Queen Elizabeth",
      type: "National Park",
      image: "/images/wildlife/queen-elizabeth-landscape.jpeg",
      alt: "Queen Elizabeth National Park",
    },
    {
      name: "Murchison Falls",
      type: "National Park",
      image: "/images/wildlife/murchison-falls-dramatic.jpeg",
      alt: "Murchison Falls",
    },
    {
      name: "Lake Bunyonyi",
      type: "Lake",
      image: "/images/wildlife/crater-lake.jpeg",
      alt: "Lake Bunyonyi",
    },
  ]

  return (
    <div className="flex flex-col">
      {/* Hero Slider Section */}
      <HeroSlider />

      {/* Featured Packages */}
      <section className="py-16 safari-pattern-bg">
        <div className="container">
          <AnimatedSection>
            <div className="text-center mb-12">
              <span className="inline-block px-3 py-1 bg-jarthaz-gold/10 text-jarthaz-gold text-sm font-medium rounded-full mb-2">
                Explore Uganda
              </span>
              <h2 className="section-heading">Our Popular Packages</h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                Explore our most sought-after travel experiences, carefully curated to showcase the best of Uganda.
              </p>
            </div>
          </AnimatedSection>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {tourPackages.map((pkg, index) => (
              <PackageCard key={pkg.id} {...pkg} delay={0.2 + index * 0.1} />
            ))}
          </div>

          <AnimatedSection delay={0.5} className="text-center mt-12">
            <Button variant="outline" size="lg" asChild className="group">
              <Link href="/destinations">
                View All Packages
                <ChevronRight size={16} className="ml-1 group-hover:translate-x-1 transition-transform" />
              </Link>
            </Button>
          </AnimatedSection>
        </div>
      </section>

      {/* Safari Divider */}
      <SafariDivider type="mountain" className="bg-jarthaz-gold/5" />

      {/* Stats Section */}
      <StatsSection />

      {/* Why Choose Us */}
      <section className="py-16">
        <div className="container">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <AnimatedSection direction="right">
              <div>
                <span className="inline-block px-3 py-1 bg-jarthaz-gold/10 text-jarthaz-gold text-sm font-medium rounded-full mb-2">
                  Why Choose Us
                </span>
                <h2 className="section-heading">Why Choose Jarthaz Tours?</h2>
                <p className="text-muted-foreground mb-8">
                  We are committed to providing exceptional travel experiences that connect our clients to Uganda's
                  natural beauty, cultural heritage, and warm hospitality.
                </p>

                <div className="space-y-6">
                  <motion.div
                    className="flex gap-4 p-4 rounded-lg hover:bg-jarthaz-gold/5 transition-colors"
                    whileHover={{ x: 5 }}
                  >
                    <div className="bg-jarthaz-gold/10 p-3 rounded-full h-fit">
                      <Compass className="h-6 w-6 text-jarthaz-gold" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-lg">Customized Itineraries</h3>
                      <p className="text-muted-foreground">
                        We tailor every package to meet your interests and budget.
                      </p>
                    </div>
                  </motion.div>

                  <motion.div
                    className="flex gap-4 p-4 rounded-lg hover:bg-jarthaz-gold/5 transition-colors"
                    whileHover={{ x: 5 }}
                  >
                    <div className="bg-jarthaz-gold/10 p-3 rounded-full h-fit">
                      <Users className="h-6 w-6 text-jarthaz-gold" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-lg">Experienced Guides</h3>
                      <p className="text-muted-foreground">
                        Our knowledgeable team ensures a safe and enriching experience.
                      </p>
                    </div>
                  </motion.div>

                  <motion.div
                    className="flex gap-4 p-4 rounded-lg hover:bg-jarthaz-gold/5 transition-colors"
                    whileHover={{ x: 5 }}
                  >
                    <div className="bg-jarthaz-gold/10 p-3 rounded-full h-fit">
                      <Mountain className="h-6 w-6 text-jarthaz-gold" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-lg">Sustainable Tourism</h3>
                      <p className="text-muted-foreground">
                        We prioritize eco-friendly practices and community involvement.
                      </p>
                    </div>
                  </motion.div>
                </div>
              </div>
            </AnimatedSection>

            <AnimatedSection direction="left">
              <ParallaxSection>
                <div className="relative h-[400px] rounded-lg overflow-hidden shadow-xl">
                  <Image
                    src="/images/wildlife/zebra-pair-closeup.jpeg"
                    alt="Zebras in Uganda"
                    fill
                    className="object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-jarthaz-black/50 to-transparent"></div>

                  {/* Decorative elements */}
                  <div className="absolute top-6 left-6 w-20 h-20 border-t-2 border-l-2 border-jarthaz-gold/50 rounded-tl-lg"></div>
                  <div className="absolute bottom-6 right-6 w-20 h-20 border-b-2 border-r-2 border-jarthaz-gold/50 rounded-br-lg"></div>

                  <div className="absolute bottom-4 left-4 bg-white/90 dark:bg-jarthaz-black/90 p-4 rounded-lg max-w-xs backdrop-blur-sm">
                    <h3 className="font-bold text-lg mb-1">Unforgettable Experiences</h3>
                    <p className="text-sm text-muted-foreground">
                      Create memories that will last a lifetime with our expertly guided tours.
                    </p>
                  </div>
                </div>
              </ParallaxSection>
            </AnimatedSection>
          </div>
        </div>
      </section>

      {/* Safari Divider */}
      <SafariDivider type="animal" />

      {/* Popular Destinations */}
      <section className="py-16 bg-muted/50">
        <div className="container">
          <AnimatedSection>
            <div className="text-center mb-12">
              <span className="inline-block px-3 py-1 bg-jarthaz-gold/10 text-jarthaz-gold text-sm font-medium rounded-full mb-2">
                Discover Uganda
              </span>
              <h2 className="section-heading">Popular Destinations</h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                From breathtaking landscapes to vibrant cultures, Uganda offers a treasure trove of experiences waiting
                to be discovered.
              </p>
            </div>
          </AnimatedSection>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {destinations.map((destination, index) => (
              <DestinationCard key={index} {...destination} delay={0.2 + index * 0.1} />
            ))}
          </div>

          <AnimatedSection delay={0.6} className="text-center mt-12">
            <Button variant="outline" size="lg" asChild className="group">
              <Link href="/destinations">
                Explore All Destinations
                <ChevronRight size={16} className="ml-1 group-hover:translate-x-1 transition-transform" />
              </Link>
            </Button>
          </AnimatedSection>
        </div>
      </section>

      {/* Tour Categories */}
      <section className="py-16">
        <div className="container">
          <AnimatedSection>
            <div className="text-center mb-12">
              <span className="inline-block px-3 py-1 bg-jarthaz-gold/10 text-jarthaz-gold text-sm font-medium rounded-full mb-2">
                Our Offerings
              </span>
              <h2 className="section-heading">Explore Our Tour Categories</h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                Whatever your travel style or interest, we have the perfect experience for you.
              </p>
            </div>
          </AnimatedSection>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            <AnimatedSection delay={0.2}>
              <SafariIcon
                icon={Camera}
                title="Wildlife Safaris"
                description="Experience the thrill of Uganda's wildlife in its natural splendor across our diverse national parks."
              />
            </AnimatedSection>

            <AnimatedSection delay={0.3}>
              <SafariIcon
                icon={Mountain}
                title="Adventure Tours"
                description="From white-water rafting to mountain climbing, satisfy your thirst for adventure."
              />
            </AnimatedSection>

            <AnimatedSection delay={0.4}>
              <SafariIcon
                icon={Users}
                title="Cultural Experiences"
                description="Immerse yourself in Uganda's rich cultural tapestry with visits to historic sites and local villages."
              />
            </AnimatedSection>

            <AnimatedSection delay={0.5}>
              <SafariIcon
                icon={Bird}
                title="Bird Watching"
                description="Uganda is a bird-watcher's paradise, home to over 1,000 bird species including the rare shoebill stork."
              />
            </AnimatedSection>

            <AnimatedSection delay={0.6}>
              <SafariIcon
                icon={Heart}
                title="Honeymoon Getaways"
                description="Celebrate your love with romantic escapes in Uganda's most serene and picturesque locations."
              />
            </AnimatedSection>

            <AnimatedSection delay={0.7}>
              <SafariIcon
                icon={Compass}
                title="Custom Tours"
                description="Let us create a personalized itinerary tailored to your specific interests, timeframe, and budget."
              />
            </AnimatedSection>
          </div>
        </div>
      </section>

      {/* Safari Divider */}
      <SafariDivider variant="wave" className="text-jarthaz-gold/5" />

      {/* Testimonials Section */}
      <section className="py-16 bg-jarthaz-gold/10">
        <div className="container">
          <AnimatedSection>
            <div className="text-center mb-12">
              <span className="inline-block px-3 py-1 bg-jarthaz-gold/20 text-jarthaz-gold text-sm font-medium rounded-full mb-2">
                Testimonials
              </span>
              <h2 className="section-heading">What Our Clients Say</h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                Don't just take our word for it. Here's what travelers have to say about their experiences with Jarthaz
                Tours.
              </p>
            </div>
          </AnimatedSection>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <AnimatedSection delay={0.2}>
              <motion.div
                className="bg-white dark:bg-jarthaz-black/50 p-6 rounded-lg shadow-md relative"
                whileHover={{
                  y: -5,
                  boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
                }}
              >
                <div className="absolute -top-4 left-6 text-5xl text-jarthaz-gold opacity-30">"</div>
                <p className="text-muted-foreground mb-4 relative z-10">
                  Our gorilla trekking experience with Jarthaz Tours was absolutely incredible. The guides were
                  knowledgeable, accommodations were comfortable, and seeing the gorillas up close was a dream come
                  true!
                </p>
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-jarthaz-gold/20 rounded-full flex items-center justify-center mr-3">
                    <span className="text-jarthaz-gold font-bold">JM</span>
                  </div>
                  <div>
                    <h4 className="font-semibold">James Miller</h4>
                    <p className="text-xs text-muted-foreground">United Kingdom</p>
                  </div>
                </div>

                {/* Rating stars */}
                <div className="flex mt-3">
                  {[...Array(5)].map((_, i) => (
                    <svg key={i} className="w-4 h-4 text-jarthaz-gold fill-current" viewBox="0 0 24 24">
                      <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                    </svg>
                  ))}
                </div>
              </motion.div>
            </AnimatedSection>

            <AnimatedSection delay={0.3}>
              <motion.div
                className="bg-white dark:bg-jarthaz-black/50 p-6 rounded-lg shadow-md relative"
                whileHover={{
                  y: -5,
                  boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
                }}
              >
                <div className="absolute -top-4 left-6 text-5xl text-jarthaz-gold opacity-30">"</div>
                <p className="text-muted-foreground mb-4 relative z-10">
                  The wildlife safari exceeded all our expectations. We saw lions, elephants, hippos, and so many other
                  animals. Our guide was exceptional at spotting wildlife and sharing interesting facts about each
                  species.
                </p>
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-jarthaz-gold/20 rounded-full flex items-center justify-center mr-3">
                    <span className="text-jarthaz-gold font-bold">SC</span>
                  </div>
                  <div>
                    <h4 className="font-semibold">Sarah Chen</h4>
                    <p className="text-xs text-muted-foreground">Canada</p>
                  </div>
                </div>

                {/* Rating stars */}
                <div className="flex mt-3">
                  {[...Array(5)].map((_, i) => (
                    <svg key={i} className="w-4 h-4 text-jarthaz-gold fill-current" viewBox="0 0 24 24">
                      <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                    </svg>
                  ))}
                </div>
              </motion.div>
            </AnimatedSection>

            <AnimatedSection delay={0.4}>
              <motion.div
                className="bg-white dark:bg-jarthaz-black/50 p-6 rounded-lg shadow-md relative"
                whileHover={{
                  y: -5,
                  boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
                }}
              >
                <div className="absolute -top-4 left-6 text-5xl text-jarthaz-gold opacity-30">"</div>
                <p className="text-muted-foreground mb-4 relative z-10">
                  Jarthaz Tours created a perfect custom itinerary for our family. The cultural experiences were
                  authentic, and our children loved the adventure activities. Uganda is a beautiful country with warm,
                  welcoming people.
                </p>
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-jarthaz-gold/20 rounded-full flex items-center justify-center mr-3">
                    <span className="text-jarthaz-gold font-bold">DP</span>
                  </div>
                  <div>
                    <h4 className="font-semibold">David Peterson</h4>
                    <p className="text-xs text-muted-foreground">United States</p>
                  </div>
                </div>

                {/* Rating stars */}
                <div className="flex mt-3">
                  {[...Array(5)].map((_, i) => (
                    <svg key={i} className="w-4 h-4 text-jarthaz-gold fill-current" viewBox="0 0 24 24">
                      <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                    </svg>
                  ))}
                </div>
              </motion.div>
            </AnimatedSection>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-jarthaz-black text-white relative overflow-hidden">
        <div className="absolute inset-0 z-0">
          <Image
            src="/images/wildlife/queen-elizabeth-landscape.jpeg"
            alt="Uganda landscape"
            fill
            className="object-cover opacity-30"
          />
        </div>

        {/* Decorative elements */}
        <div className="absolute top-10 left-10 w-40 h-40 border-t-2 border-l-2 border-jarthaz-gold/30 rounded-tl-3xl"></div>
        <div className="absolute bottom-10 right-10 w-40 h-40 border-b-2 border-r-2 border-jarthaz-gold/30 rounded-br-3xl"></div>

        <div className="container relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            <AnimatedSection direction="right">
              <div>
                <span className="inline-block px-3 py-1 bg-jarthaz-gold text-jarthaz-black text-sm font-medium rounded-full mb-2">
                  Start Your Journey
                </span>
                <h2 className="text-3xl font-bold tracking-tight mb-4 text-white">Ready to Explore Uganda?</h2>
                <p className="mb-6 text-white/90">
                  Let's craft your perfect Ugandan adventure together. Contact us for inquiries, bookings, and
                  personalized packages.
                </p>
                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Button size="lg" variant="safari" asChild className="cta-button group">
                    <Link href="/contact" className="flex items-center">
                      Plan Your Trip Now
                      <ArrowRight className="ml-2 group-hover:translate-x-1 transition-transform" />
                    </Link>
                  </Button>
                </motion.div>
              </div>
            </AnimatedSection>

            <AnimatedSection direction="left">
              <ParallaxSection>
                <div className="relative h-[300px] rounded-lg overflow-hidden shadow-xl">
                  <Image
                    src="/images/honeymoon/hot-air-balloon-safari-murchison-uganda2.webp"
                    alt="Hot air balloon safari over Uganda landscape"
                    fill
                    className="object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-jarthaz-black/50 to-transparent"></div>
                  <div className="absolute bottom-0 left-0 right-0 p-4 text-white">
                    <h3 className="font-bold text-xl mb-1">Discover the Pearl of Africa</h3>
                    <p className="text-sm text-white/80">
                      Uganda offers a unique blend of wildlife, adventure, culture, and natural beauty.
                    </p>
                  </div>
                </div>
              </ParallaxSection>
            </AnimatedSection>
          </div>
        </div>
      </section>
    </div>
  )
}
