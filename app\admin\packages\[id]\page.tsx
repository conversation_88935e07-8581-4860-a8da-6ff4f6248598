import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { ArrowLeft, Save, Trash2, Plus, Edit } from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { Checkbox } from "@/components/ui/checkbox"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

// This would normally fetch data based on the ID
export default function EditPackagePage({ params }: { params: { id: string } }) {
  const isNew = params.id === "new"

  // Mock data for an existing package
  const packageData = isNew
    ? {
        id: "",
        title: "",
        description: "",
        image: "/placeholder.svg?height=400&width=600",
        featured: false,
        highlights: [],
        itineraries: [],
      }
    : {
        id: "gorilla-trekking",
        title: "Gorilla Trekking Adventures",
        description: "A once-in-a-lifetime journey to meet Uganda's gentle giants in their natural habitat.",
        image: "/placeholder.svg?height=400&width=600",
        featured: true,
        highlights: [
          "Face-to-face encounter with mountain gorillas",
          "Expert guides and trackers",
          "Stunning forest landscapes",
          "Cultural experiences with local communities",
          "Comfortable accommodation in scenic locations",
        ],
        itineraries: [
          {
            title: "Bwindi 3 Days Gorilla Trekking",
            days: [
              {
                title: "Day 1: Journey to Bwindi",
                description:
                  "Your adventure begins with an early morning pickup from your hotel in Kampala or Entebbe. Travel southwest through Uganda's beautiful countryside, with a stop at the equator for photos and souvenir shopping. Enjoy lunch en route and continue to Bwindi Impenetrable National Park. Arrive in the evening at your accommodation near the park, where you'll have dinner and a briefing about the next day's gorilla trekking experience.",
              },
              {
                title: "Day 2: Gorilla Trekking",
                description:
                  "After an early breakfast, head to the park headquarters for a briefing from the rangers about gorilla trekking protocols. Then, embark on your trek through the forest in search of a gorilla family. The trek can take anywhere from 30 minutes to several hours, depending on the gorillas' location. Once you find them, you'll spend a magical hour observing these magnificent creatures in their natural habitat. Return to your lodge for lunch and relaxation. In the afternoon, you can opt for a community visit to learn about local culture and traditions.",
              },
              {
                title: "Day 3: Return to Kampala/Entebbe",
                description:
                  "After breakfast, begin your journey back to Kampala or Entebbe. Enjoy the scenic drive with stops for photo opportunities and lunch. You'll also have the chance for cultural encounters along the way. Arrive in Kampala or Entebbe in the evening, where you'll be dropped off at your hotel or the airport for your onward journey.",
              },
            ],
          },
        ],
      }

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" asChild>
            <Link href="/admin/packages">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h2 className="text-3xl font-bold tracking-tight">
            {isNew ? "Add New Package" : `Edit ${packageData.title}`}
          </h2>
        </div>
        <div className="flex items-center gap-2">
          {!isNew && (
            <Button variant="destructive">
              <Trash2 className="mr-2 h-4 w-4" /> Delete
            </Button>
          )}
          <Button>
            <Save className="mr-2 h-4 w-4" /> Save
          </Button>
        </div>
      </div>

      <Tabs defaultValue="general" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="highlights">Highlights</TabsTrigger>
          <TabsTrigger value="itineraries">Itineraries</TabsTrigger>
          <TabsTrigger value="media">Media</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-6">
          <Card>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div className="grid gap-2">
                  <Label htmlFor="title">Package Title</Label>
                  <Input id="title" defaultValue={packageData.title} placeholder="Enter package title" />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    defaultValue={packageData.description}
                    placeholder="Enter package description"
                    rows={5}
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="featured" defaultChecked={packageData.featured} />
                  <Label htmlFor="featured">Featured Package</Label>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-4">
                  <Label>Featured Image</Label>
                  <div className="relative aspect-video overflow-hidden rounded-md border">
                    <Image
                      src={packageData.image || "/placeholder.svg"}
                      alt="Featured image"
                      fill
                      className="object-cover"
                    />
                  </div>
                  <Button variant="outline" className="w-full">
                    Change Image
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="space-y-4">
                  <Label>SEO Settings</Label>
                  <div className="grid gap-2">
                    <Label htmlFor="seo-title" className="text-sm">
                      SEO Title
                    </Label>
                    <Input id="seo-title" placeholder="SEO title" />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="seo-description" className="text-sm">
                      Meta Description
                    </Label>
                    <Textarea id="seo-description" placeholder="Meta description" rows={3} />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="highlights" className="space-y-6">
          <Card>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label>Package Highlights</Label>
                  <Button variant="outline" size="sm">
                    Add Highlight
                  </Button>
                </div>
                <div className="space-y-2">
                  {packageData.highlights.map((highlight, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <Input defaultValue={highlight} className="flex-1" />
                      <Button variant="ghost" size="icon">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                  {packageData.highlights.length === 0 && (
                    <p className="text-sm text-muted-foreground">No highlights added yet.</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="itineraries" className="space-y-6">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Itineraries</h3>
            <Button>
              <Plus className="mr-2 h-4 w-4" /> Add Itinerary
            </Button>
          </div>

          {packageData.itineraries.map((itinerary, index) => (
            <Card key={index}>
              <CardContent className="pt-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="grid gap-2 flex-1 mr-4">
                      <Label htmlFor={`itinerary-${index}-title`}>Itinerary Title</Label>
                      <Input
                        id={`itinerary-${index}-title`}
                        defaultValue={itinerary.title}
                        placeholder="Enter itinerary title"
                      />
                    </div>
                    <Button variant="destructive" size="sm">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <Label>Days</Label>
                      <Button variant="outline" size="sm">
                        Add Day
                      </Button>
                    </div>

                    {itinerary.days.map((day, dayIndex) => (
                      <div key={dayIndex} className="space-y-2 border p-4 rounded-md">
                        <div className="flex items-center justify-between">
                          <Label htmlFor={`day-${index}-${dayIndex}-title`}>Day Title</Label>
                          <Button variant="ghost" size="icon">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                        <Input
                          id={`day-${index}-${dayIndex}-title`}
                          defaultValue={day.title}
                          placeholder="Enter day title"
                          className="mb-2"
                        />
                        <Label htmlFor={`day-${index}-${dayIndex}-description`}>Description</Label>
                        <Textarea
                          id={`day-${index}-${dayIndex}-description`}
                          defaultValue={day.description}
                          placeholder="Enter day description"
                          rows={3}
                        />
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}

          {packageData.itineraries.length === 0 && (
            <Card>
              <CardContent className="pt-6 text-center py-12">
                <p className="text-muted-foreground">No itineraries added yet. Click "Add Itinerary" to create one.</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="media" className="space-y-6">
          <Card>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label>Gallery Images</Label>
                  <Button>Upload Images</Button>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {[1, 2, 3, 4, 5, 6].map((item) => (
                    <div key={item} className="relative aspect-square rounded-md overflow-hidden border group">
                      <Image
                        src="/placeholder.svg?height=200&width=200"
                        alt={`Gallery image ${item}`}
                        fill
                        className="object-cover"
                      />
                      <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                        <Button variant="secondary" size="icon" className="h-8 w-8">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="destructive" size="icon" className="h-8 w-8">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
