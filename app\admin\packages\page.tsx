import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardDescription, <PERSON><PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Plus, Search, Edit, Trash2 } from "lucide-react"
import Link from "next/link"
import Image from "next/image"

// Mock data for packages
const packages = [
  {
    id: "gorilla-trekking",
    title: "Gorilla Trekking Adventures",
    description: "A once-in-a-lifetime journey to meet Uganda's gentle giants in their natural habitat.",
    image: "/placeholder.svg?height=400&width=600",
    featured: true,
  },
  {
    id: "wildlife-safaris",
    title: "Wildlife Safaris",
    description: "Experience the thrill of Uganda's wildlife in its natural splendor.",
    image: "/placeholder.svg?height=400&width=600",
    featured: true,
  },
  {
    id: "adventure-escapades",
    title: "Adventure Escapades",
    description: "For adrenaline seekers, Uganda offers endless excitement.",
    image: "/placeholder.svg?height=400&width=600",
    featured: true,
  },
  {
    id: "cultural-tours",
    title: "Cultural and Heritage Tours",
    description: "Immerse yourself in Uganda's rich cultural tapestry.",
    image: "/placeholder.svg?height=400&width=600",
    featured: false,
  },
  {
    id: "bird-watching",
    title: "Bird Watching Expeditions",
    description: "Uganda is a bird-watcher's paradise, home to over 1,000 bird species.",
    image: "/placeholder.svg?height=400&width=600",
    featured: false,
  },
  {
    id: "honeymoon-getaways",
    title: "Honeymoon Getaways",
    description: "Celebrate your love with romantic escapes in Uganda's serene locations.",
    image: "/placeholder.svg?height=400&width=600",
    featured: false,
  },
]

export default function PackagesPage() {
  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Tour Packages</h2>
        <Button asChild>
          <Link href="/admin/packages/new">
            <Plus className="mr-2 h-4 w-4" /> Add Package
          </Link>
        </Button>
      </div>

      <div className="flex items-center gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input type="search" placeholder="Search packages..." className="pl-8" />
        </div>
        <Button variant="outline">Filter</Button>
      </div>

      <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {packages.map((pkg) => (
          <Card key={pkg.id} className="overflow-hidden">
            <div className="relative h-48">
              <Image src={pkg.image || "/placeholder.svg"} alt={pkg.title} fill className="object-cover" />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
              {pkg.featured && (
                <div className="absolute top-2 right-2 bg-jarthaz-gold text-black text-xs font-medium px-2 py-1 rounded">
                  Featured
                </div>
              )}
            </div>
            <CardHeader>
              <CardTitle className="line-clamp-1">{pkg.title}</CardTitle>
              <CardDescription className="line-clamp-2">{pkg.description}</CardDescription>
            </CardHeader>
            <CardFooter className="flex justify-between">
              <Button variant="outline" size="sm" asChild>
                <Link href={`/admin/packages/${pkg.id}`}>
                  <Edit className="mr-2 h-4 w-4" /> Edit
                </Link>
              </Button>
              <Button variant="destructive" size="sm">
                <Trash2 className="mr-2 h-4 w-4" /> Delete
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  )
}
