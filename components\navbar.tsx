"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { Button } from "@/components/ui/button"
import { Menu, X, ChevronDown } from "lucide-react"
import { usePathname } from "next/navigation"
import { motion } from "framer-motion"

export default function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)
  const [isPackagesOpen, setIsPackagesOpen] = useState(false)
  const pathname = usePathname()

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 50) {
        setIsScrolled(true)
      } else {
        setIsScrolled(false)
      }
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  const navLinks = [
    { name: "Home", href: "/" },
    { name: "About", href: "/about" },
    { name: "Destinations", href: "/destinations" },
    { name: "Packages", href: "#", hasDropdown: true },
    { name: "Contact", href: "/contact" },
  ]

  const packageLinks = [
    { name: "Gorilla Trekking", href: "/packages/gorilla-trekking" },
    { name: "Wildlife Safaris", href: "/packages/wildlife-safaris" },
    { name: "Adventure Escapades", href: "/packages/adventure-escapades" },
    { name: "Cultural Tours", href: "/packages/cultural-tours" },
    { name: "Bird Watching", href: "/packages/bird-watching" },
    { name: "Honeymoon Getaways", href: "/packages/honeymoon-getaways" },
  ]

  const navVariants = {
    hidden: { opacity: 0, y: -20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        staggerChildren: 0.1,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: -10 },
    visible: { opacity: 1, y: 0 },
  }

  return (
    <header
      className={`sticky top-0 z-50 w-full transition-all duration-300 ${
        isScrolled ? "bg-white/95 backdrop-blur-md shadow-md dark:bg-jarthaz-black/95" : "bg-transparent"
      }`}
    >
      <div className="container flex h-16 items-center justify-between">
        <Link href="/" className="flex items-center space-x-2">
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            className="relative h-10 w-auto"
          >
            <Image src="/images/logo.png" alt="Jarthaz Tours Logo" width={120} height={40} className="object-contain" />
          </motion.div>
        </Link>

        {/* Desktop Navigation */}
        <motion.nav className="hidden md:flex gap-1" initial="hidden" animate="visible" variants={navVariants}>
          {navLinks.map((link) => (
            <motion.div key={link.name} variants={itemVariants} className="relative">
              {link.hasDropdown ? (
                <>
                  <button
                    className={`nav-link flex items-center px-3 py-2 text-sm font-medium transition-colors hover:text-jarthaz-gold ${
                      pathname.includes("/packages") ? "text-jarthaz-gold" : "text-muted-foreground"
                    }`}
                    onClick={() => setIsPackagesOpen(!isPackagesOpen)}
                  >
                    {link.name} <ChevronDown size={16} className="ml-1" />
                  </button>
                  {isPackagesOpen && (
                    <div className="absolute top-full left-0 mt-1 w-56 rounded-md bg-white shadow-lg dark:bg-jarthaz-black p-2 animate-fade-in">
                      {packageLinks.map((pkg) => (
                        <Link
                          key={pkg.name}
                          href={pkg.href}
                          className="block px-4 py-2 text-sm rounded-md hover:bg-jarthaz-gold/10 hover:text-jarthaz-gold"
                          onClick={() => setIsPackagesOpen(false)}
                        >
                          {pkg.name}
                        </Link>
                      ))}
                    </div>
                  )}
                </>
              ) : (
                <Link
                  href={link.href}
                  className={`nav-link px-3 py-2 text-sm font-medium transition-colors hover:text-jarthaz-gold ${
                    pathname === link.href ? "text-jarthaz-gold" : "text-muted-foreground"
                  }`}
                >
                  {link.name}
                </Link>
              )}
            </motion.div>
          ))}
        </motion.nav>

        <motion.div
          className="hidden md:block"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
        >
          <Button asChild className="bg-jarthaz-gold hover:bg-jarthaz-darkgold text-black">
            <Link href="/contact">Book Now</Link>
          </Button>
        </motion.div>

        {/* Mobile Menu Button */}
        <button className="md:hidden" onClick={toggleMenu} aria-label="Toggle Menu">
          {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
        </button>
      </div>

      {/* Mobile Navigation */}
      {isMenuOpen && (
        <motion.div
          className="md:hidden bg-white dark:bg-jarthaz-black shadow-lg"
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: "auto" }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.3 }}
        >
          <div className="container py-4 flex flex-col space-y-4">
            {navLinks.map((link) => (
              link.hasDropdown ? (
                <div key={link.name} className="py-2">
                  <div
                    className="text-sm font-medium mb-2 text-muted-foreground"
                    onClick={() => setIsPackagesOpen(!isPackagesOpen)}
                  >
                    {link.name} <ChevronDown size={16} className="inline ml-1" />
                  </div>
                  {isPackagesOpen && (
                    <div className="pl-4 space-y-2 border-l-2 border-jarthaz-gold/30">
                      {packageLinks.map((pkg) => (
                        <Link
                          key={pkg.name}
                          href={pkg.href}
                          className="block text-sm text-muted-foreground hover:text-jarthaz-gold"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          {pkg.name}
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              ) : (
                <Link
                  key={link.name}
                  href={link.href}
                  className={`text-sm font-medium transition-colors hover:text-jarthaz-gold ${
                    pathname === link.href ? "text-jarthaz-gold" : "text-muted-foreground"
                  }`}
                  onClick={() => setIsMenuOpen(false)}
                >
                  {link.name}
                </Link>
              )
            ))}
            <Button asChild className="w-full bg-jarthaz-gold hover:bg-jarthaz-darkgold text-black">
              <Link href="/contact" onClick={() => setIsMenuOpen(false)}>
                Book Now
              </Link>
            </Button>
          </div>
        </motion.div>
      )}
    </header>
  )
}
