"use client"

import type React from "react"

import { useRef } from "react"
import { motion, useScroll, useTransform } from "framer-motion"

interface ParallaxSectionProps {
  children: React.ReactNode
  speed?: number
  className?: string
  direction?: "up" | "down" | "left" | "right"
}

export default function ParallaxSection({
  children,
  speed = 0.5,
  className = "",
  direction = "up",
}: ParallaxSectionProps) {
  const ref = useRef(null)
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"],
  })

  const defaultX = useTransform(scrollYProgress, [0, 1], [0, 0])
  const defaultY = useTransform(scrollYProgress, [0, 1], [0, 0])

  let x = defaultX
  let y = defaultY

  if (direction === "up") {
    y = useTransform(scrollYProgress, [0, 1], [100 * speed, -100 * speed])
  } else if (direction === "down") {
    y = useTransform(scrollYProgress, [0, 1], [-100 * speed, 100 * speed])
  } else if (direction === "left") {
    x = useTransform(scrollYProgress, [0, 1], [100 * speed, -100 * speed])
  } else if (direction === "right") {
    x = useTransform(scrollYProgress, [0, 1], [-100 * speed, 100 * speed])
  }

  return (
    <div ref={ref} className={`overflow-hidden ${className}`}>
      <motion.div style={{ x, y }}>{children}</motion.div>
    </div>
  )
}
