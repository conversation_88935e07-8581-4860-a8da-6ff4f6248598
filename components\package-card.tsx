"use client"

import Image from "next/image"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Clock, Users, ChevronRight } from "lucide-react"
import AnimatedSection from "@/components/animated-section"
import { motion } from "framer-motion"

interface PackageCardProps {
  id: string
  title: string
  description: string
  image: string
  alt: string
  delay?: number
  duration?: string
  groupSize?: string
}

export default function PackageCard({
  id,
  title,
  description,
  image,
  alt,
  delay = 0,
  duration,
  groupSize,
}: PackageCardProps) {
  return (
    <AnimatedSection delay={delay}>
      <motion.div
        className="bg-white dark:bg-jarthaz-black/50 rounded-lg overflow-hidden shadow-md h-full flex flex-col card-hover"
        whileHover={{ y: -5, boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)" }}
      >
        <div className="relative h-48 image-hover-zoom">
          <Image src={image} alt={alt} fill className="object-cover" />
          <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
        </div>
        <div className="p-5 flex-grow flex flex-col">
          <h3 className="font-bold text-xl mb-2">{title}</h3>
          <p className="text-muted-foreground text-sm mb-4">{description}</p>

          {(duration || groupSize) && (
            <div className="flex flex-wrap gap-3 mb-4 mt-auto">
              {duration && (
                <div className="flex items-center text-xs text-muted-foreground">
                  <Clock size={14} className="mr-1 text-jarthaz-gold" />
                  <span>{duration}</span>
                </div>
              )}
              {groupSize && (
                <div className="flex items-center text-xs text-muted-foreground">
                  <Users size={14} className="mr-1 text-jarthaz-gold" />
                  <span>{groupSize}</span>
                </div>
              )}
            </div>
          )}

          <Button variant="outline" size="sm" className="w-full group mt-auto" asChild>
            <Link href={`/packages/${id}`}>
              View Package
              <ChevronRight size={16} className="ml-1 group-hover:translate-x-1 transition-transform" />
            </Link>
          </Button>
        </div>
      </motion.div>
    </AnimatedSection>
  )
}
