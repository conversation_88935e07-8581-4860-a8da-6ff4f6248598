import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Save, Edit, Home, Info, Phone, Globe } from "lucide-react"
import Link from "next/link"
import Image from "next/image"

export default function ContentPage() {
  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Website Content</h2>
        <Button>
          <Save className="mr-2 h-4 w-4" /> Save All Changes
        </Button>
      </div>

      <Tabs defaultValue="home" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="home">
            <Home className="mr-2 h-4 w-4" /> Home
          </TabsTrigger>
          <TabsTrigger value="about">
            <Info className="mr-2 h-4 w-4" /> About
          </TabsTrigger>
          <TabsTrigger value="contact">
            <Phone className="mr-2 h-4 w-4" /> Contact
          </TabsTrigger>
          <TabsTrigger value="global">
            <Globe className="mr-2 h-4 w-4" /> Global
          </TabsTrigger>
        </TabsList>

        <TabsContent value="home" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Hero Section</CardTitle>
              <CardDescription>Edit the main hero section content on the homepage</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="hero-title">Hero Title</Label>
                <Input id="hero-title" defaultValue="Your Gateway to Explore Uganda" placeholder="Enter hero title" />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="hero-description">Hero Description</Label>
                <Textarea
                  id="hero-description"
                  defaultValue="Discover the beauty and diversity of Uganda with Jarthaz Tours. From misty mountains to expansive savannahs, experience the Pearl of Africa."
                  placeholder="Enter hero description"
                  rows={3}
                />
              </div>
              <div className="grid gap-2">
                <Label>Hero Background</Label>
                <div className="relative aspect-video overflow-hidden rounded-md border">
                  <Image
                    src="/placeholder.svg?height=400&width=800"
                    alt="Hero background"
                    fill
                    className="object-cover"
                  />
                </div>
                <Button variant="outline" className="mt-2">
                  Change Image
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Featured Packages Section</CardTitle>
              <CardDescription>Edit the featured packages section on the homepage</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="packages-title">Section Title</Label>
                <Input id="packages-title" defaultValue="Our Popular Packages" placeholder="Enter section title" />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="packages-description">Section Description</Label>
                <Textarea
                  id="packages-description"
                  defaultValue="Explore our most sought-after travel experiences, carefully curated to showcase the best of Uganda."
                  placeholder="Enter section description"
                  rows={3}
                />
              </div>
              <div className="grid gap-2">
                <Label>Featured Packages</Label>
                <div className="border rounded-md p-4">
                  <p className="text-sm text-muted-foreground mb-4">
                    Select packages to feature on the homepage. You can manage all packages in the
                    <Link href="/admin/packages" className="text-primary font-medium mx-1">
                      Packages
                    </Link>
                    section.
                  </p>
                  <Button variant="outline">Manage Featured Packages</Button>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Why Choose Us Section</CardTitle>
              <CardDescription>Edit the "Why Choose Us" section on the homepage</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="why-title">Section Title</Label>
                <Input id="why-title" defaultValue="Why Choose Jarthaz Tours?" placeholder="Enter section title" />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="why-description">Section Description</Label>
                <Textarea
                  id="why-description"
                  defaultValue="We are committed to providing exceptional travel experiences that connect our clients to Uganda's natural beauty, cultural heritage, and warm hospitality."
                  placeholder="Enter section description"
                  rows={3}
                />
              </div>
              <div className="grid gap-2">
                <Label>Features</Label>
                <div className="space-y-4">
                  <div className="border rounded-md p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">Feature 1</h4>
                      <Button variant="ghost" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                    </div>
                    <p className="text-sm text-muted-foreground">Customized Itineraries</p>
                  </div>
                  <div className="border rounded-md p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">Feature 2</h4>
                      <Button variant="ghost" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                    </div>
                    <p className="text-sm text-muted-foreground">Experienced Guides</p>
                  </div>
                  <div className="border rounded-md p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">Feature 3</h4>
                      <Button variant="ghost" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                    </div>
                    <p className="text-sm text-muted-foreground">Sustainable Tourism</p>
                  </div>
                  <Button variant="outline">Add Feature</Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="about" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>About Page Content</CardTitle>
              <CardDescription>Edit the main content on the About page</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="about-hero-title">Hero Title</Label>
                <Input id="about-hero-title" defaultValue="About Jarthaz Tours" placeholder="Enter hero title" />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="about-hero-description">Hero Description</Label>
                <Textarea
                  id="about-hero-description"
                  defaultValue="Learn about our story, mission, and commitment to showcasing the beauty of Uganda."
                  placeholder="Enter hero description"
                  rows={3}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="our-story-title">Our Story Title</Label>
                <Input id="our-story-title" defaultValue="Our Story" placeholder="Enter section title" />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="our-story-content">Our Story Content</Label>
                <Textarea
                  id="our-story-content"
                  defaultValue="Jarthaz Tours began with a simple but powerful idea: to share the incredible beauty and cultural richness of Uganda with the world. Founded by passionate travel enthusiasts, we are committed to showcasing the 'Pearl of Africa' as a top-tier destination for travelers seeking authentic, immersive experiences."
                  placeholder="Enter content"
                  rows={6}
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Vision & Mission</CardTitle>
              <CardDescription>Edit the vision and mission statements</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="vision">Our Vision</Label>
                <Textarea
                  id="vision"
                  defaultValue="To be the most trusted and innovative travel partner, inspiring unforgettable journeys across Uganda and beyond."
                  placeholder="Enter vision statement"
                  rows={3}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="mission">Our Mission</Label>
                <Textarea
                  id="mission"
                  defaultValue="To provide exceptional travel experiences that connect our clients to Uganda's natural beauty, cultural heritage, and warm hospitality, while promoting sustainable tourism and community development."
                  placeholder="Enter mission statement"
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="contact" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Contact Information</CardTitle>
              <CardDescription>Edit your business contact information</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="address">Address</Label>
                <Input id="address" defaultValue="Kataza, Kampala, Uganda" placeholder="Enter business address" />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="phone">Phone Number</Label>
                <Input id="phone" defaultValue="+256-788-359-512" placeholder="Enter phone number" />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="whatsapp">WhatsApp</Label>
                <Input id="whatsapp" defaultValue="+256-754-820-874" placeholder="Enter WhatsApp number" />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="email">Email</Label>
                <Input id="email" defaultValue="<EMAIL>" placeholder="Enter email address" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Social Media Links</CardTitle>
              <CardDescription>Edit your social media profiles</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="facebook">Facebook</Label>
                <Input
                  id="facebook"
                  defaultValue="https://www.facebook.com/Jarthaztoursandtravel"
                  placeholder="Enter Facebook URL"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="twitter">Twitter</Label>
                <Input id="twitter" defaultValue="https://x.com/JarthazTours" placeholder="Enter Twitter URL" />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="instagram">Instagram</Label>
                <Input
                  id="instagram"
                  defaultValue="https://www.instagram.com/jarthaztoursandtravel/"
                  placeholder="Enter Instagram URL"
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="global" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Global Settings</CardTitle>
              <CardDescription>Edit settings that apply across the entire website</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="site-title">Website Title</Label>
                <Input
                  id="site-title"
                  defaultValue="Jarthaz Tours - Your Gateway to Explore Uganda"
                  placeholder="Enter website title"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="site-description">Website Description</Label>
                <Textarea
                  id="site-description"
                  defaultValue="Discover the beauty and diversity of Uganda with Jarthaz Tours. We are your trusted partner in creating unforgettable travel experiences across the Pearl of Africa."
                  placeholder="Enter website description"
                  rows={3}
                />
              </div>
              <div className="grid gap-2">
                <Label>Logo</Label>
                <div className="flex items-center gap-4 p-4 border rounded-md">
                  <div className="relative h-12 w-32">
                    <Image src="/images/logo.png" alt="Jarthaz Tours Logo" fill className="object-contain" />
                  </div>
                  <Button variant="outline">Change Logo</Button>
                </div>
              </div>
              <div className="grid gap-2">
                <Label>Favicon</Label>
                <div className="flex items-center gap-4 p-4 border rounded-md">
                  <div className="relative h-10 w-10 border rounded">
                    <Image src="/images/logo.png" alt="Favicon" fill className="object-contain" />
                  </div>
                  <Button variant="outline">Change Favicon</Button>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Footer Content</CardTitle>
              <CardDescription>Edit the content that appears in the website footer</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="footer-description">Footer Description</Label>
                <Textarea
                  id="footer-description"
                  defaultValue="Your trusted partner in creating unforgettable travel experiences across the Pearl of Africa."
                  placeholder="Enter footer description"
                  rows={3}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="copyright">Copyright Text</Label>
                <Input
                  id="copyright"
                  defaultValue="© 2023 Jarthaz Tours. All rights reserved."
                  placeholder="Enter copyright text"
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
