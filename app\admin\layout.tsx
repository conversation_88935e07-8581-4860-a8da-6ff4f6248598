import type React from "react"
import { redirect } from "next/navigation"
import AdminNav from "@/components/admin/admin-nav"

// This is a simple auth check - in a real app, you would use a proper auth system
async function getUser() {
  // This is a placeholder for actual authentication
  // In a real app, you would check session/cookies here
  const isLoggedIn = true // For demo purposes
  return isLoggedIn ? { name: "Admin User" } : null
}

export default async function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const user = await getUser()

  if (!user) {
    redirect("/admin/login")
  }

  return (
    <div className="flex min-h-screen flex-col">
      <header className="sticky top-0 z-30 border-b bg-background">
        <div className="container flex h-16 items-center justify-between py-4">
          <h1 className="text-xl font-bold">Jarthaz Tours Admin</h1>
        </div>
      </header>
      <div className="container flex-1 items-start md:grid md:grid-cols-[220px_minmax(0,1fr)] md:gap-6 lg:grid-cols-[240px_minmax(0,1fr)] lg:gap-10">
        <AdminNav />
        <main className="relative py-6 md:gap-10 lg:py-8">{children}</main>
      </div>
    </div>
  )
}
