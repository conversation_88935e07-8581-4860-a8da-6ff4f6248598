interface SafariDividerProps {
  variant?: "wave" | "mountain" | "animal" | "line"
  className?: string
}

export default function SafariDivider({ variant = "line", className = "" }: SafariDividerProps) {
  switch (variant) {
    case "wave":
      return (
        <div className={`w-full overflow-hidden ${className}`}>
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 100" className="text-background fill-current">
            <path d="M0,64L48,69.3C96,75,192,85,288,80C384,75,480,53,576,53.3C672,53,768,75,864,80C960,85,1056,75,1152,64C1248,53,1344,43,1392,37.3L1440,32L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path>
          </svg>
        </div>
      )
    case "mountain":
      return (
        <div className={`w-full overflow-hidden ${className}`}>
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 100" className="text-background fill-current">
            <path d="M0,32L48,37.3C96,43,192,53,288,69.3C384,85,480,107,576,101.3C672,96,768,64,864,58.7C960,53,1056,75,1152,80C1248,85,1344,75,1392,69.3L1440,64L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path>
          </svg>
        </div>
      )
    case "animal":
      return (
        <div className={`w-full h-16 relative ${className}`}>
          <div className="absolute bottom-0 left-0 w-full h-1 bg-jarthaz-gold/30"></div>
          <div className="safari-silhouette-icon absolute bottom-0 left-1/2 transform -translate-x-1/2"></div>
        </div>
      )
    case "line":
    default:
      return <div className={`safari-divider ${className}`}></div>
  }
}
