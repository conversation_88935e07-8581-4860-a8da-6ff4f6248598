import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, email, phone, tourType, message } = body

    // Validate required fields
    if (!name || !email || !message) {
      return NextResponse.json(
        { error: 'Name, email, and message are required' },
        { status: 400 }
      )
    }

    // In a real application, you would:
    // 1. Use a service like Nodemailer, SendGrid, or Resend to send emails
    // 2. Store the inquiry in a database
    // 3. Send confirmation emails to both the customer and business
    
    // For now, we'll simulate the email sending process
    const emailData = {
      to: '<EMAIL>',
      from: email,
      subject: `New Contact Form Submission - ${tourType || 'General Inquiry'}`,
      html: `
        <h2>New Contact Form Submission</h2>
        <p><strong>Name:</strong> ${name}</p>
        <p><strong>Email:</strong> ${email}</p>
        <p><strong>Phone:</strong> ${phone || 'Not provided'}</p>
        <p><strong>Tour Type:</strong> ${tourType || 'Not specified'}</p>
        <p><strong>Message:</strong></p>
        <p>${message}</p>
        <hr>
        <p><em>This message was sent from the Jarthaz Tours contact form.</em></p>
      `
    }

    // Log the email data (in production, this would actually send the email)
    console.log('Email would be <NAME_EMAIL>:', emailData)

    // Simulate successful email sending
    return NextResponse.json(
      { 
        success: true, 
        message: 'Your message has been sent successfully. We will get back to you soon!' 
      },
      { status: 200 }
    )

  } catch (error) {
    console.error('Contact form submission error:', error)
    return NextResponse.json(
      { error: 'Failed to send message. Please try again later.' },
      { status: 500 }
    )
  }
}
