"use client"

import Image from "next/image"
import Link from "next/link"
import { motion } from "framer-motion"
import { Button } from "@/components/ui/button"
import { Check, ArrowLeft, Calendar, MapPin, Clock, Users, Star } from "lucide-react"
import AnimatedSection from "@/components/animated-section"
import SafariDivider from "@/components/safari-divider"

// Animation variants
const fadeIn = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.6 } },
}

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
}

const itemFadeIn = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
}

export default function AdventureEscapadesPage() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative h-[70vh] flex items-center overflow-hidden">
        <div className="absolute inset-0 z-0">
          <Image
            src="/images/adventures/rafting-waves.jpeg"
            alt="White-water rafting on the Nile River"
            fill
            className="object-cover brightness-[0.65] scale-[1.02]"
            priority
          />
          <div className="absolute inset-0 bg-black/20" />
        </div>

        {/* Safari pattern overlay */}
        <div className="absolute inset-0 bg-[url('/placeholder.svg?height=200&width=200')] bg-repeat opacity-10 mix-blend-overlay" />

        {/* Decorative elements */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5, duration: 1 }}
          className="absolute top-20 right-20 w-32 h-32 border-4 border-jarthaz-gold/30 rounded-full"
        />
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.7, duration: 1 }}
          className="absolute bottom-20 left-20 w-24 h-24 border-4 border-jarthaz-gold/20 rounded-full"
        />

        <div className="container relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-3xl space-y-6"
          >
            <Link href="/destinations" className="inline-flex items-center text-white/90 hover:text-white mb-4 group">
              <ArrowLeft size={16} className="mr-2 transition-transform group-hover:-translate-x-1" />
              <span className="text-sm font-medium">Back to Destinations</span>
            </Link>

            <div className="space-y-4">
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5 }}
                className="inline-block px-3 py-1 rounded-full bg-jarthaz-gold/90 text-black text-xs font-semibold"
              >
                PREMIUM EXPERIENCE
              </motion.div>

              <motion.h1
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.7, delay: 0.2 }}
                className="text-4xl font-bold tracking-tighter sm:text-5xl md:text-6xl text-white drop-shadow-md"
              >
                Adventure Escapades
              </motion.h1>

              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.7, delay: 0.4 }}
                className="text-xl text-white/90 max-w-2xl"
              >
                For adrenaline seekers, Uganda offers endless excitement and thrilling activities in some of East
                Africa's most spectacular settings.
              </motion.p>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.7, delay: 0.6 }}
                className="flex flex-wrap gap-4 mt-4"
              >
                <div className="flex items-center gap-1 text-white/90 text-sm">
                  <Clock size={16} className="text-jarthaz-gold" />
                  <span>1-7 day packages</span>
                </div>
                <div className="flex items-center gap-1 text-white/90 text-sm">
                  <MapPin size={16} className="text-jarthaz-gold" />
                  <span>Jinja, Rwenzori, Lake Mburo</span>
                </div>
                <div className="flex items-center gap-1 text-white/90 text-sm">
                  <Users size={16} className="text-jarthaz-gold" />
                  <span>All experience levels</span>
                </div>
              </motion.div>
            </div>
          </motion.div>
        </div>

        {/* Backdrop blur panel */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="absolute bottom-0 left-0 right-0 bg-black/30 backdrop-blur-md h-24 z-10"
        >
          <div className="container h-full flex items-center justify-between">
            <div className="flex items-center gap-6">
              <div className="flex items-center gap-1">
                <Star className="h-5 w-5 fill-jarthaz-gold text-jarthaz-gold" />
                <Star className="h-5 w-5 fill-jarthaz-gold text-jarthaz-gold" />
                <Star className="h-5 w-5 fill-jarthaz-gold text-jarthaz-gold" />
                <Star className="h-5 w-5 fill-jarthaz-gold text-jarthaz-gold" />
                <Star className="h-5 w-5 fill-jarthaz-gold/80 text-jarthaz-gold" />
                <span className="ml-2 text-white text-sm font-medium">4.8/5 from 120+ reviews</span>
              </div>
              <div className="hidden md:block w-px h-8 bg-white/20"></div>
              <div className="hidden md:flex items-center gap-1 text-white/90 text-sm">
                <Calendar size={16} className="text-jarthaz-gold" />
                <span>Available year-round</span>
              </div>
            </div>
            <div className="hidden md:block">
              <Button className="bg-jarthaz-gold hover:bg-jarthaz-gold/90 text-black" size="lg" asChild>
                <Link href="/contact">Book This Adventure</Link>
              </Button>
            </div>
          </div>
        </motion.div>
      </section>

      {/* Main Content */}
      <section className="py-16">
        <div className="container">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {/* Left Content */}
            <div className="lg:col-span-2 space-y-16">
              <AnimatedSection>
                <div className="space-y-6">
                  <h2 className="text-3xl font-bold tracking-tight mb-6 flex items-center">
                    <span className="w-8 h-8 rounded-full bg-jarthaz-gold/20 flex items-center justify-center mr-3">
                      <span className="text-jarthaz-gold text-sm font-bold">01</span>
                    </span>
                    About Our Adventure Packages
                  </h2>
                  <div className="text-muted-foreground space-y-4">
                    <p>
                      Uganda isn't just about wildlife and cultural experiences—it's also an adventure lover's paradise.
                      From the thundering rapids of the Nile River to the towering peaks of the Rwenzori Mountains, our
                      adventure packages offer heart-pumping activities in some of East Africa's most spectacular
                      settings.
                    </p>
                    <p>
                      Whether you're seeking the adrenaline rush of white-water rafting, the challenge of mountain
                      climbing, or the thrill of bungee jumping, our experienced guides ensure your safety while
                      delivering unforgettable adventure experiences.
                    </p>
                    <p>
                      These adventure activities can be enjoyed as standalone experiences or combined with wildlife
                      safaris and cultural tours for a comprehensive Ugandan experience.
                    </p>
                  </div>
                </div>
              </AnimatedSection>

              <AnimatedSection>
                <div className="relative h-[400px] rounded-xl overflow-hidden group">
                  <Image
                    src="/images/adventures/rafting-nile.jpeg"
                    alt="White-water rafting on the Nile"
                    fill
                    className="object-cover transition-transform duration-700 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent"></div>
                  <div className="absolute bottom-0 left-0 p-6 text-white">
                    <h3 className="text-2xl font-bold mb-2">Experience the Thrill</h3>
                    <p className="text-white/80 max-w-lg">
                      Our adventure activities are designed to get your adrenaline pumping while showcasing Uganda's
                      natural beauty
                    </p>
                  </div>
                </div>
              </AnimatedSection>

              <SafariDivider />

              <AnimatedSection>
                <div>
                  <h2 className="text-3xl font-bold tracking-tight mb-8 flex items-center">
                    <span className="w-8 h-8 rounded-full bg-jarthaz-gold/20 flex items-center justify-center mr-3">
                      <span className="text-jarthaz-gold text-sm font-bold">02</span>
                    </span>
                    Adventure Activities
                  </h2>

                  <motion.div
                    variants={staggerContainer}
                    initial="hidden"
                    whileInView="visible"
                    viewport={{ once: true, margin: "-100px" }}
                    className="space-y-8"
                  >
                    <motion.div
                      variants={itemFadeIn}
                      className="bg-muted/50 p-6 rounded-xl border border-border/50 hover:border-jarthaz-gold/30 transition-colors duration-300 shadow-sm"
                    >
                      <div className="flex flex-col md:flex-row gap-6">
                        <div className="flex-1">
                          <h3 className="font-bold text-xl mb-3 text-jarthaz-gold">White-Water Rafting on the Nile</h3>
                          <p className="text-muted-foreground mb-3">
                            Experience world-class rapids on the mighty Nile River near Jinja. With options ranging from
                            grade 3 to grade 5 rapids, there's something for both beginners and experienced rafters.
                          </p>
                          <div className="grid grid-cols-2 gap-4 mt-4">
                            <div className="flex items-start gap-2">
                              <Clock size={16} className="text-jarthaz-gold mt-1" />
                              <div>
                                <p className="font-medium text-sm">Duration</p>
                                <p className="text-sm text-muted-foreground">Half-day or full-day options</p>
                              </div>
                            </div>
                            <div className="flex items-start gap-2">
                              <Check size={16} className="text-jarthaz-gold mt-1" />
                              <div>
                                <p className="font-medium text-sm">Includes</p>
                                <p className="text-sm text-muted-foreground">Guides, equipment, lunch, refreshments</p>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="md:w-1/3 h-48 md:h-auto relative rounded-lg overflow-hidden group">
                          <Image
                            src="/images/adventures/rafting-waves.jpeg"
                            alt="Thrilling white-water rafting experience"
                            fill
                            className="object-cover transition-transform duration-700 group-hover:scale-110"
                          />
                        </div>
                      </div>
                    </motion.div>

                    <motion.div
                      variants={itemFadeIn}
                      className="bg-muted/50 p-6 rounded-xl border border-border/50 hover:border-jarthaz-gold/30 transition-colors duration-300 shadow-sm"
                    >
                      <div className="flex flex-col md:flex-row gap-6">
                        <div className="md:w-1/3 h-48 md:h-auto relative rounded-lg overflow-hidden group">
                          <Image
                            src="/images/adventures/bungee-jumping.jpeg"
                            alt="Bungee jumping over the Nile River"
                            fill
                            className="object-cover transition-transform duration-700 group-hover:scale-110"
                          />
                        </div>
                        <div className="flex-1">
                          <h3 className="font-bold text-xl mb-3 text-jarthaz-gold">Bungee Jumping</h3>
                          <p className="text-muted-foreground mb-3">
                            Take the plunge with a 44-meter bungee jump over the Nile River. This heart-stopping
                            activity offers spectacular views and an unforgettable adrenaline rush.
                          </p>
                          <div className="grid grid-cols-2 gap-4 mt-4">
                            <div className="flex items-start gap-2">
                              <MapPin size={16} className="text-jarthaz-gold mt-1" />
                              <div>
                                <p className="font-medium text-sm">Location</p>
                                <p className="text-sm text-muted-foreground">Jinja</p>
                              </div>
                            </div>
                            <div className="flex items-start gap-2">
                              <Check size={16} className="text-jarthaz-gold mt-1" />
                              <div>
                                <p className="font-medium text-sm">Includes</p>
                                <p className="text-sm text-muted-foreground">Safety briefing, equipment, certificate</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </motion.div>

                    <motion.div
                      variants={itemFadeIn}
                      className="bg-muted/50 p-6 rounded-xl border border-border/50 hover:border-jarthaz-gold/30 transition-colors duration-300 shadow-sm"
                    >
                      <h3 className="font-bold text-xl mb-3 text-jarthaz-gold">Mountain Climbing</h3>
                      <p className="text-muted-foreground mb-3">
                        Challenge yourself with a climb up the Rwenzori Mountains (the "Mountains of the Moon") or Mount
                        Elgon. These multi-day treks offer stunning landscapes, unique flora, and a true sense of
                        accomplishment.
                      </p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                        <div className="flex items-start gap-2">
                          <Clock size={16} className="text-jarthaz-gold mt-1" />
                          <div>
                            <p className="font-medium text-sm">Duration</p>
                            <p className="text-sm text-muted-foreground">3-10 days depending on the route</p>
                          </div>
                        </div>
                        <div className="flex items-start gap-2">
                          <Check size={16} className="text-jarthaz-gold mt-1" />
                          <div>
                            <p className="font-medium text-sm">Includes</p>
                            <p className="text-sm text-muted-foreground">
                              Guides, porters, accommodation, meals, park fees
                            </p>
                          </div>
                        </div>
                      </div>
                    </motion.div>

                    <motion.div
                      variants={itemFadeIn}
                      className="bg-muted/50 p-6 rounded-xl border border-border/50 hover:border-jarthaz-gold/30 transition-colors duration-300 shadow-sm"
                    >
                      <div className="flex flex-col md:flex-row gap-6">
                        <div className="flex-1">
                          <h3 className="font-bold text-xl mb-3 text-jarthaz-gold">Zip-lining</h3>
                          <p className="text-muted-foreground mb-3">
                            Soar through the air on zip-lines over the Nile, Mabira Forest, or Lake Bunyonyi. These
                            exhilarating rides offer a bird's-eye view of Uganda's beautiful landscapes.
                          </p>
                          <div className="grid grid-cols-2 gap-4 mt-4">
                            <div className="flex items-start gap-2">
                              <Clock size={16} className="text-jarthaz-gold mt-1" />
                              <div>
                                <p className="font-medium text-sm">Duration</p>
                                <p className="text-sm text-muted-foreground">1-3 hours</p>
                              </div>
                            </div>
                            <div className="flex items-start gap-2">
                              <Check size={16} className="text-jarthaz-gold mt-1" />
                              <div>
                                <p className="font-medium text-sm">Includes</p>
                                <p className="text-sm text-muted-foreground">Safety briefing, equipment, guides</p>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="md:w-1/3 h-48 md:h-auto relative rounded-lg overflow-hidden group">
                          <Image
                            src="/images/adventures/zip-lining.jpeg"
                            alt="Zip-lining through Uganda's lush forests"
                            fill
                            className="object-cover transition-transform duration-700 group-hover:scale-110"
                          />
                        </div>
                      </div>
                    </motion.div>

                    <motion.div
                      variants={itemFadeIn}
                      className="bg-muted/50 p-6 rounded-xl border border-border/50 hover:border-jarthaz-gold/30 transition-colors duration-300 shadow-sm"
                    >
                      <div className="flex flex-col md:flex-row gap-6">
                        <div className="md:w-1/3 h-48 md:h-auto relative rounded-lg overflow-hidden group">
                          <Image
                            src="/images/adventures/tubing-rapids.jpeg"
                            alt="Tubing and kayaking on the Nile"
                            fill
                            className="object-cover transition-transform duration-700 group-hover:scale-110"
                          />
                        </div>
                        <div className="flex-1">
                          <h3 className="font-bold text-xl mb-3 text-jarthaz-gold">Kayaking and Tubing</h3>
                          <p className="text-muted-foreground mb-3">
                            Navigate the Nile's waters in a kayak or enjoy a more relaxed tubing experience. These
                            activities offer different perspectives of the river and its surroundings.
                          </p>
                          <div className="grid grid-cols-2 gap-4 mt-4">
                            <div className="flex items-start gap-2">
                              <MapPin size={16} className="text-jarthaz-gold mt-1" />
                              <div>
                                <p className="font-medium text-sm">Location</p>
                                <p className="text-sm text-muted-foreground">Jinja</p>
                              </div>
                            </div>
                            <div className="flex items-start gap-2">
                              <Check size={16} className="text-jarthaz-gold mt-1" />
                              <div>
                                <p className="font-medium text-sm">Includes</p>
                                <p className="text-sm text-muted-foreground">Equipment, guides, refreshments</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </motion.div>

                    <motion.div
                      variants={itemFadeIn}
                      className="bg-muted/50 p-6 rounded-xl border border-border/50 hover:border-jarthaz-gold/30 transition-colors duration-300 shadow-sm"
                    >
                      <div className="flex flex-col md:flex-row gap-6">
                        <div className="flex-1">
                          <h3 className="font-bold text-xl mb-3 text-jarthaz-gold">Horseback Safari</h3>
                          <p className="text-muted-foreground mb-3">
                            Experience the unique thrill of a horseback safari in Lake Mburo National Park, where you
                            can ride alongside zebras, impalas, and other wildlife in their natural habitat.
                          </p>
                          <div className="grid grid-cols-2 gap-4 mt-4">
                            <div className="flex items-start gap-2">
                              <Clock size={16} className="text-jarthaz-gold mt-1" />
                              <div>
                                <p className="font-medium text-sm">Duration</p>
                                <p className="text-sm text-muted-foreground">2-4 hours</p>
                              </div>
                            </div>
                            <div className="flex items-start gap-2">
                              <Check size={16} className="text-jarthaz-gold mt-1" />
                              <div>
                                <p className="font-medium text-sm">Includes</p>
                                <p className="text-sm text-muted-foreground">Horses, guides, equipment, refreshments</p>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="md:w-1/3 h-48 md:h-auto relative rounded-lg overflow-hidden group">
                          <Image
                            src="/images/adventures/horseback-safari.jpeg"
                            alt="Horseback safari with zebras in Lake Mburo"
                            fill
                            className="object-cover transition-transform duration-700 group-hover:scale-110"
                          />
                        </div>
                      </div>
                    </motion.div>
                  </motion.div>
                </div>
              </AnimatedSection>

              <SafariDivider />

              <AnimatedSection>
                <div>
                  <h2 className="text-3xl font-bold tracking-tight mb-8 flex items-center">
                    <span className="w-8 h-8 rounded-full bg-jarthaz-gold/20 flex items-center justify-center mr-3">
                      <span className="text-jarthaz-gold text-sm font-bold">03</span>
                    </span>
                    Featured Itineraries
                  </h2>

                  <div className="space-y-10 mt-6">
                    <div className="bg-muted/30 p-8 rounded-xl border border-border/50 relative overflow-hidden">
                      {/* Decorative element */}
                      <div className="absolute top-0 right-0 w-32 h-32 bg-jarthaz-gold/10 rounded-bl-[100px]"></div>

                      <h3 className="font-bold text-2xl mb-6 text-jarthaz-gold">2-Day Jinja Adventure Package</h3>
                      <div className="space-y-6">
                        <motion.div
                          variants={fadeIn}
                          initial="hidden"
                          whileInView="visible"
                          viewport={{ once: true }}
                          className="bg-background p-5 rounded-lg shadow-sm"
                        >
                          <h4 className="font-semibold mb-2 flex items-center">
                            <span className="w-6 h-6 rounded-full bg-jarthaz-gold/20 flex items-center justify-center mr-2 text-xs text-jarthaz-gold font-bold">
                              1
                            </span>
                            Day 1: Kampala to Jinja - White-Water Rafting
                          </h4>
                          <p className="text-muted-foreground">
                            Morning departure from Kampala to Jinja (approximately 2 hours). Upon arrival, receive a
                            safety briefing before embarking on an exhilarating white-water rafting experience on the
                            Nile. Navigate through thrilling rapids with professional guides ensuring your safety. Enjoy
                            a riverside lunch during the activity. In the evening, check into your accommodation in
                            Jinja for dinner and relaxation.
                          </p>
                        </motion.div>

                        <motion.div
                          variants={fadeIn}
                          initial="hidden"
                          whileInView="visible"
                          viewport={{ once: true }}
                          className="bg-background p-5 rounded-lg shadow-sm"
                        >
                          <h4 className="font-semibold mb-2 flex items-center">
                            <span className="w-6 h-6 rounded-full bg-jarthaz-gold/20 flex items-center justify-center mr-2 text-xs text-jarthaz-gold font-bold">
                              2
                            </span>
                            Day 2: Bungee Jumping and Return to Kampala
                          </h4>
                          <p className="text-muted-foreground">
                            After breakfast, choose between bungee jumping, quad biking, or kayaking for your morning
                            adventure activity. After lunch, visit the source of the Nile, where the mighty river begins
                            its journey from Lake Victoria. In the afternoon, return to Kampala, arriving in the early
                            evening.
                          </p>
                        </motion.div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
                      <motion.div
                        variants={fadeIn}
                        initial="hidden"
                        whileInView="visible"
                        viewport={{ once: true }}
                        className="relative h-64 rounded-xl overflow-hidden group"
                      >
                        <Image
                          src="/images/adventures/river-tubing.webp"
                          alt="Relaxing river tubing experience"
                          fill
                          className="object-cover transition-transform duration-700 group-hover:scale-110"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent flex items-end">
                          <div className="p-6 text-white">
                            <h4 className="font-bold text-lg">River Tubing</h4>
                            <p className="text-sm text-white/90">Relax and enjoy the gentle currents of the Nile</p>
                          </div>
                        </div>
                      </motion.div>
                      <motion.div
                        variants={fadeIn}
                        initial="hidden"
                        whileInView="visible"
                        viewport={{ once: true }}
                        transition={{ delay: 0.2 }}
                        className="relative h-64 rounded-xl overflow-hidden group"
                      >
                        <Image
                          src="/images/adventures/hippos-mburo.png"
                          alt="Hippos in Lake Mburo National Park"
                          fill
                          className="object-cover transition-transform duration-700 group-hover:scale-110"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent flex items-end">
                          <div className="p-6 text-white">
                            <h4 className="font-bold text-lg">Wildlife Encounters</h4>
                            <p className="text-sm text-white/90">
                              Spot hippos and other wildlife during your adventures
                            </p>
                          </div>
                        </div>
                      </motion.div>
                    </div>

                    <div className="bg-muted/30 p-8 rounded-xl border border-border/50 relative overflow-hidden">
                      {/* Decorative element */}
                      <div className="absolute top-0 right-0 w-32 h-32 bg-jarthaz-gold/10 rounded-bl-[100px]"></div>

                      <h3 className="font-bold text-2xl mb-6 text-jarthaz-gold">
                        5-Day Rwenzori Mountain Climbing Expedition
                      </h3>
                      <div className="space-y-4">
                        <motion.div
                          variants={fadeIn}
                          initial="hidden"
                          whileInView="visible"
                          viewport={{ once: true }}
                          className="bg-background p-5 rounded-lg shadow-sm"
                        >
                          <h4 className="font-semibold mb-2 flex items-center">
                            <span className="w-6 h-6 rounded-full bg-jarthaz-gold/20 flex items-center justify-center mr-2 text-xs text-jarthaz-gold font-bold">
                              1
                            </span>
                            Day 1: Kampala to Kasese
                          </h4>
                          <p className="text-muted-foreground">
                            Drive from Kampala to Kasese, near the Rwenzori Mountains. Check into your accommodation and
                            receive a briefing about the climb. Rest and prepare for the adventure ahead.
                          </p>
                        </motion.div>

                        <motion.div
                          variants={fadeIn}
                          initial="hidden"
                          whileInView="visible"
                          viewport={{ once: true }}
                          transition={{ delay: 0.1 }}
                          className="bg-background p-5 rounded-lg shadow-sm"
                        >
                          <h4 className="font-semibold mb-2 flex items-center">
                            <span className="w-6 h-6 rounded-full bg-jarthaz-gold/20 flex items-center justify-center mr-2 text-xs text-jarthaz-gold font-bold">
                              2
                            </span>
                            Day 2: Start of Trek - Nyakalengija to Nyabitaba Hut
                          </h4>
                          <p className="text-muted-foreground">
                            Begin your trek from Nyakalengija (1,615m) to Nyabitaba Hut (2,651m). The trail takes you
                            through the montane forest zone, home to various primates and birds. Overnight at Nyabitaba
                            Hut.
                          </p>
                        </motion.div>

                        <motion.div
                          variants={fadeIn}
                          initial="hidden"
                          whileInView="visible"
                          viewport={{ once: true }}
                          transition={{ delay: 0.2 }}
                          className="bg-background p-5 rounded-lg shadow-sm"
                        >
                          <h4 className="font-semibold mb-2 flex items-center">
                            <span className="w-6 h-6 rounded-full bg-jarthaz-gold/20 flex items-center justify-center mr-2 text-xs text-jarthaz-gold font-bold">
                              3
                            </span>
                            Day 3: Nyabitaba to John Matte Hut
                          </h4>
                          <p className="text-muted-foreground">
                            Trek from Nyabitaba Hut to John Matte Hut (3,505m). Cross the Kurt Shafer Bridge and enter
                            the bamboo-mimulopsis zone. The trail offers stunning views of the mountains. Overnight at
                            John Matte Hut.
                          </p>
                        </motion.div>

                        <motion.div
                          variants={fadeIn}
                          initial="hidden"
                          whileInView="visible"
                          viewport={{ once: true }}
                          transition={{ delay: 0.3 }}
                          className="bg-background p-5 rounded-lg shadow-sm"
                        >
                          <h4 className="font-semibold mb-2 flex items-center">
                            <span className="w-6 h-6 rounded-full bg-jarthaz-gold/20 flex items-center justify-center mr-2 text-xs text-jarthaz-gold font-bold">
                              4
                            </span>
                            Day 4: John Matte Hut to Nyabitaba Hut
                          </h4>
                          <p className="text-muted-foreground">
                            Begin your descent, returning to Nyabitaba Hut. Enjoy different perspectives of the
                            landscape as you make your way down. Overnight at Nyabitaba Hut.
                          </p>
                        </motion.div>

                        <motion.div
                          variants={fadeIn}
                          initial="hidden"
                          whileInView="visible"
                          viewport={{ once: true }}
                          transition={{ delay: 0.4 }}
                          className="bg-background p-5 rounded-lg shadow-sm"
                        >
                          <h4 className="font-semibold mb-2 flex items-center">
                            <span className="w-6 h-6 rounded-full bg-jarthaz-gold/20 flex items-center justify-center mr-2 text-xs text-jarthaz-gold font-bold">
                              5
                            </span>
                            Day 5: Nyabitaba to Nyakalengija and Return to Kampala
                          </h4>
                          <p className="text-muted-foreground">
                            Complete your descent to Nyakalengija. After lunch, begin the journey back to Kampala,
                            arriving in the evening.
                          </p>
                        </motion.div>
                      </div>
                      <p className="text-sm text-muted-foreground mt-6 italic">
                        Note: Longer Rwenzori treks (7-10 days) are available for those who wish to reach higher peaks,
                        including Margherita Peak (5,109m), the highest point in Uganda.
                      </p>
                    </div>
                  </div>
                </div>
              </AnimatedSection>

              <SafariDivider />

              <AnimatedSection>
                <div>
                  <h2 className="text-3xl font-bold tracking-tight mb-8 flex items-center">
                    <span className="w-8 h-8 rounded-full bg-jarthaz-gold/20 flex items-center justify-center mr-3">
                      <span className="text-jarthaz-gold text-sm font-bold">04</span>
                    </span>
                    Safety Information
                  </h2>
                  <div className="space-y-6 bg-muted/30 p-8 rounded-xl border border-border/50 relative overflow-hidden">
                    {/* Decorative element */}
                    <div className="absolute top-0 left-0 w-32 h-32 bg-jarthaz-gold/10 rounded-br-[100px]"></div>

                    <div className="relative">
                      <p className="text-muted-foreground">
                        Your safety is our top priority. All our adventure activities are conducted with professional
                        guides and quality equipment that meets international safety standards. Before each activity,
                        you'll receive a comprehensive safety briefing and appropriate training.
                      </p>

                      <div className="mt-6">
                        <h3 className="font-semibold text-lg mb-3">Physical Requirements</h3>
                        <p className="text-muted-foreground">
                          Different activities have different physical requirements. White-water rafting and bungee
                          jumping are suitable for most people with reasonable fitness levels, while mountain climbing
                          requires good physical condition and some preparation. Our team can help you choose activities
                          that match your fitness level and experience.
                        </p>
                      </div>

                      <div className="mt-6">
                        <h3 className="font-semibold text-lg mb-3">What to Bring</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          <motion.div
                            variants={staggerContainer}
                            initial="hidden"
                            whileInView="visible"
                            viewport={{ once: true }}
                            className="space-y-2"
                          >
                            <motion.div variants={itemFadeIn} className="flex items-start gap-2">
                              <Check size={16} className="text-jarthaz-gold mt-1" />
                              <span className="text-muted-foreground">Comfortable, quick-drying clothes</span>
                            </motion.div>
                            <motion.div variants={itemFadeIn} className="flex items-start gap-2">
                              <Check size={16} className="text-jarthaz-gold mt-1" />
                              <span className="text-muted-foreground">Swimwear for water activities</span>
                            </motion.div>
                            <motion.div variants={itemFadeIn} className="flex items-start gap-2">
                              <Check size={16} className="text-jarthaz-gold mt-1" />
                              <span className="text-muted-foreground">
                                Secure footwear (sports sandals/hiking boots)
                              </span>
                            </motion.div>
                            <motion.div variants={itemFadeIn} className="flex items-start gap-2">
                              <Check size={16} className="text-jarthaz-gold mt-1" />
                              <span className="text-muted-foreground">Sun protection (hat, sunglasses, sunscreen)</span>
                            </motion.div>
                          </motion.div>

                          <motion.div
                            variants={staggerContainer}
                            initial="hidden"
                            whileInView="visible"
                            viewport={{ once: true }}
                            className="space-y-2"
                          >
                            <motion.div variants={itemFadeIn} className="flex items-start gap-2">
                              <Check size={16} className="text-jarthaz-gold mt-1" />
                              <span className="text-muted-foreground">Small backpack for personal items</span>
                            </motion.div>
                            <motion.div variants={itemFadeIn} className="flex items-start gap-2">
                              <Check size={16} className="text-jarthaz-gold mt-1" />
                              <span className="text-muted-foreground">Camera (waterproof for water activities)</span>
                            </motion.div>
                            <motion.div variants={itemFadeIn} className="flex items-start gap-2">
                              <Check size={16} className="text-jarthaz-gold mt-1" />
                              <span className="text-muted-foreground">Personal medications</span>
                            </motion.div>
                            <motion.div variants={itemFadeIn} className="flex items-start gap-2">
                              <Check size={16} className="text-jarthaz-gold mt-1" />
                              <span className="text-muted-foreground">
                                For mountain climbing: warm clothing, rain gear
                              </span>
                            </motion.div>
                          </motion.div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </AnimatedSection>

              <SafariDivider />

              <AnimatedSection>
                <div>
                  <h2 className="text-3xl font-bold tracking-tight mb-8 flex items-center">
                    <span className="w-8 h-8 rounded-full bg-jarthaz-gold/20 flex items-center justify-center mr-3">
                      <span className="text-jarthaz-gold text-sm font-bold">05</span>
                    </span>
                    Guest Testimonials
                  </h2>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <motion.div
                      variants={fadeIn}
                      initial="hidden"
                      whileInView="visible"
                      viewport={{ once: true }}
                      className="bg-muted/30 p-6 rounded-xl border border-border/50"
                    >
                      <div className="flex items-center gap-1 mb-3">
                        <Star className="h-4 w-4 fill-jarthaz-gold text-jarthaz-gold" />
                        <Star className="h-4 w-4 fill-jarthaz-gold text-jarthaz-gold" />
                        <Star className="h-4 w-4 fill-jarthaz-gold text-jarthaz-gold" />
                        <Star className="h-4 w-4 fill-jarthaz-gold text-jarthaz-gold" />
                        <Star className="h-4 w-4 fill-jarthaz-gold text-jarthaz-gold" />
                      </div>
                      <p className="italic text-muted-foreground mb-4">
                        "The white-water rafting experience on the Nile was absolutely incredible! The guides were
                        professional and made us feel safe while still having an amazing adventure. Definitely the
                        highlight of our Uganda trip!"
                      </p>
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-muted flex items-center justify-center text-muted-foreground font-medium">
                          SJ
                        </div>
                        <div>
                          <p className="font-medium text-sm">Sarah Johnson</p>
                          <p className="text-xs text-muted-foreground">London, UK</p>
                        </div>
                      </div>
                    </motion.div>

                    <motion.div
                      variants={fadeIn}
                      initial="hidden"
                      whileInView="visible"
                      viewport={{ once: true }}
                      transition={{ delay: 0.2 }}
                      className="bg-muted/30 p-6 rounded-xl border border-border/50"
                    >
                      <div className="flex items-center gap-1 mb-3">
                        <Star className="h-4 w-4 fill-jarthaz-gold text-jarthaz-gold" />
                        <Star className="h-4 w-4 fill-jarthaz-gold text-jarthaz-gold" />
                        <Star className="h-4 w-4 fill-jarthaz-gold text-jarthaz-gold" />
                        <Star className="h-4 w-4 fill-jarthaz-gold text-jarthaz-gold" />
                        <Star className="h-4 w-4 fill-jarthaz-gold/30 text-jarthaz-gold" />
                      </div>
                      <p className="italic text-muted-foreground mb-4">
                        "Our 5-day Rwenzori trek was challenging but incredibly rewarding. The landscapes were
                        breathtaking, and our guides were knowledgeable and supportive. Jarthaz Tours took care of all
                        the details, making it a smooth experience."
                      </p>
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-muted flex items-center justify-center text-muted-foreground font-medium">
                          MT
                        </div>
                        <div>
                          <p className="font-medium text-sm">Michael Thompson</p>
                          <p className="text-xs text-muted-foreground">Sydney, Australia</p>
                        </div>
                      </div>
                    </motion.div>

                    <motion.div
                      variants={fadeIn}
                      initial="hidden"
                      whileInView="visible"
                      viewport={{ once: true }}
                      transition={{ delay: 0.3 }}
                      className="bg-muted/30 p-6 rounded-xl border border-border/50"
                    >
                      <div className="flex items-center gap-1 mb-3">
                        <Star className="h-4 w-4 fill-jarthaz-gold text-jarthaz-gold" />
                        <Star className="h-4 w-4 fill-jarthaz-gold text-jarthaz-gold" />
                        <Star className="h-4 w-4 fill-jarthaz-gold text-jarthaz-gold" />
                        <Star className="h-4 w-4 fill-jarthaz-gold text-jarthaz-gold" />
                        <Star className="h-4 w-4 fill-jarthaz-gold text-jarthaz-gold" />
                      </div>
                      <p className="italic text-muted-foreground mb-4">
                        "The horseback safari in Lake Mburo was magical! Riding alongside zebras and antelopes was a
                        unique experience I'll never forget. The horses were well-trained and the guides were
                        excellent."
                      </p>
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-muted flex items-center justify-center text-muted-foreground font-medium">
                          EL
                        </div>
                        <div>
                          <p className="font-medium text-sm">Emma Lewis</p>
                          <p className="text-xs text-muted-foreground">Toronto, Canada</p>
                        </div>
                      </div>
                    </motion.div>

                    <motion.div
                      variants={fadeIn}
                      initial="hidden"
                      whileInView="visible"
                      viewport={{ once: true }}
                      transition={{ delay: 0.4 }}
                      className="bg-muted/30 p-6 rounded-xl border border-border/50"
                    >
                      <div className="flex items-center gap-1 mb-3">
                        <Star className="h-4 w-4 fill-jarthaz-gold text-jarthaz-gold" />
                        <Star className="h-4 w-4 fill-jarthaz-gold text-jarthaz-gold" />
                        <Star className="h-4 w-4 fill-jarthaz-gold text-jarthaz-gold" />
                        <Star className="h-4 w-4 fill-jarthaz-gold text-jarthaz-gold" />
                        <Star className="h-4 w-4 fill-jarthaz-gold/30 text-jarthaz-gold" />
                      </div>
                      <p className="italic text-muted-foreground mb-4">
                        "Bungee jumping over the Nile was terrifying and exhilarating at the same time! The staff was
                        professional and the views were incredible. Jarthaz Tours made the whole experience seamless."
                      </p>
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-muted flex items-center justify-center text-muted-foreground font-medium">
                          DP
                        </div>
                        <div>
                          <p className="font-medium text-sm">David Parker</p>
                          <p className="text-xs text-muted-foreground">Chicago, USA</p>
                        </div>
                      </div>
                    </motion.div>
                  </div>
                </div>
              </AnimatedSection>
            </div>

            {/* Right Sidebar */}
            <div className="space-y-8">
              <div className="lg:sticky lg:top-24">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  className="bg-muted/50 p-6 rounded-xl border border-border/50 shadow-sm"
                >
                  <h3 className="text-xl font-bold mb-6 text-jarthaz-gold">Book This Adventure</h3>
                  <div className="space-y-4 mb-6">
                    <div className="flex justify-between items-center pb-2 border-b border-border/50">


                    </div>
                    <div className="flex justify-between items-center pb-2 border-b border-border/50">
                      <span className="text-sm font-medium">Duration</span>
                      <span className="text-sm">1-7 days</span>
                    </div>
                    <div className="flex justify-between items-center pb-2 border-b border-border/50">
                      <span className="text-sm font-medium">Group Size</span>
                      <span className="text-sm">2-12 people</span>
                    </div>
                    <div className="flex justify-between items-center pb-2 border-b border-border/50">
                      <span className="text-sm font-medium">Difficulty</span>
                      <span className="text-sm">Easy to Challenging</span>
                    </div>
                  </div>
                  <Button className="w-full bg-jarthaz-gold hover:bg-jarthaz-gold/90 text-black mb-4" size="lg" asChild>
                    <Link href="/contact">Book Now</Link>
                  </Button>
                  <Button variant="outline" className="w-full" size="lg" asChild>
                    <Link href="/contact">Request Custom Itinerary</Link>
                  </Button>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.4 }}
                  className="mt-8 bg-muted/50 p-6 rounded-xl border border-border/50 shadow-sm"
                >
                  <h3 className="text-xl font-bold mb-4">Adventure Highlights</h3>
                  <motion.ul
                    variants={staggerContainer}
                    initial="hidden"
                    whileInView="visible"
                    viewport={{ once: true }}
                    className="space-y-3"
                  >
                    <motion.li variants={itemFadeIn} className="flex items-start gap-2">
                      <Check size={16} className="text-jarthaz-gold mt-1" />
                      <span className="text-sm text-muted-foreground">World-class white-water rafting on the Nile</span>
                    </motion.li>
                    <motion.li variants={itemFadeIn} className="flex items-start gap-2">
                      <Check size={16} className="text-jarthaz-gold mt-1" />
                      <span className="text-sm text-muted-foreground">Thrilling bungee jumping experiences</span>
                    </motion.li>
                    <motion.li variants={itemFadeIn} className="flex items-start gap-2">
                      <Check size={16} className="text-jarthaz-gold mt-1" />
                      <span className="text-sm text-muted-foreground">
                        Mountain climbing in the Rwenzori "Mountains of the Moon"
                      </span>
                    </motion.li>
                    <motion.li variants={itemFadeIn} className="flex items-start gap-2">
                      <Check size={16} className="text-jarthaz-gold mt-1" />
                      <span className="text-sm text-muted-foreground">Zip-lining over spectacular landscapes</span>
                    </motion.li>
                    <motion.li variants={itemFadeIn} className="flex items-start gap-2">
                      <Check size={16} className="text-jarthaz-gold mt-1" />
                      <span className="text-sm text-muted-foreground">Professional guides and quality equipment</span>
                    </motion.li>
                    <motion.li variants={itemFadeIn} className="flex items-start gap-2">
                      <Check size={16} className="text-jarthaz-gold mt-1" />
                      <span className="text-sm text-muted-foreground">Horseback safaris alongside wildlife</span>
                    </motion.li>
                  </motion.ul>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.6 }}
                  className="mt-8"
                >
                  <div className="relative h-60 rounded-xl overflow-hidden group">
                    <Image
                      src="/images/adventures/zebras-mburo.jpeg"
                      alt="Zebras in Lake Mburo National Park"
                      fill
                      className="object-cover transition-transform duration-700 group-hover:scale-110"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent flex items-end">
                      <div className="p-6 text-white">
                        <h4 className="font-bold">Wildlife Encounters</h4>
                        <p className="text-sm text-white/90">
                          Many of our adventure activities offer opportunities to spot wildlife
                        </p>
                      </div>
                    </div>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.8 }}
                  className="mt-8 bg-muted/50 p-6 rounded-xl border border-border/50 shadow-sm"
                >
                  <h3 className="text-xl font-bold mb-4">Best Time for Adventures</h3>
                  <p className="text-sm text-muted-foreground">
                    Most adventure activities are available year-round, but the best times are during the dry seasons:
                  </p>
                  <div className="mt-3 space-y-2">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-jarthaz-gold"></div>
                      <span className="text-sm">December to February</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-jarthaz-gold"></div>
                      <span className="text-sm">June to September</span>
                    </div>
                  </div>
                  <p className="mt-4 text-sm text-muted-foreground">
                    For mountain climbing, the driest months (December-February and June-August) offer the best
                    conditions, though climbs are possible year-round.
                  </p>
                </motion.div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Related Packages */}
      <section className="py-16 bg-muted/50">
        <div className="container">
          <AnimatedSection>
            <h2 className="text-3xl font-bold tracking-tight mb-8">You Might Also Like</h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <motion.div
                variants={fadeIn}
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
                className="bg-background rounded-xl overflow-hidden shadow-sm group"
              >
                <div className="relative h-48 overflow-hidden">
                  <Image
                    src="/images/gorillas/gorilla-family.jpeg"
                    alt="Gorilla Trekking"
                    fill
                    className="object-cover transition-transform duration-700 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
                  <div className="absolute top-3 left-3">
                    <span className="px-2 py-1 bg-jarthaz-gold/90 text-black text-xs font-semibold rounded-full">
                      POPULAR
                    </span>
                  </div>
                </div>
                <div className="p-5">
                  <h3 className="font-bold text-lg mb-2">Gorilla Trekking</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Face-to-face encounters with mountain gorillas in Bwindi Impenetrable Forest.
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full group-hover:bg-jarthaz-gold group-hover:text-black group-hover:border-jarthaz-gold transition-colors duration-300"
                    asChild
                  >
                    <Link href="/packages/gorilla-trekking">View Package</Link>
                  </Button>
                </div>
              </motion.div>

              <motion.div
                variants={fadeIn}
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
                transition={{ delay: 0.2 }}
                className="bg-background rounded-xl overflow-hidden shadow-sm group"
              >
                <div className="relative h-48 overflow-hidden">
                  <Image
                    src="/images/adventures/hippos-mburo.png"
                    alt="Wildlife Safari"
                    fill
                    className="object-cover transition-transform duration-700 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
                </div>
                <div className="p-5">
                  <h3 className="font-bold text-lg mb-2">Wildlife Safaris</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Experience Uganda's diverse wildlife in its natural splendor.
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full group-hover:bg-jarthaz-gold group-hover:text-black group-hover:border-jarthaz-gold transition-colors duration-300"
                    asChild
                  >
                    <Link href="/packages/wildlife-safaris">View Package</Link>
                  </Button>
                </div>
              </motion.div>

              <motion.div
                variants={fadeIn}
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
                transition={{ delay: 0.4 }}
                className="bg-background rounded-xl overflow-hidden shadow-sm group"
              >
                <div className="relative h-48 overflow-hidden">
                  <Image
                    src="/images/adventures/horseback-safari.jpeg"
                    alt="Cultural Tours"
                    fill
                    className="object-cover transition-transform duration-700 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
                </div>
                <div className="p-5">
                  <h3 className="font-bold text-lg mb-2">Cultural Tours</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Immerse yourself in Uganda's rich cultural heritage and traditions.
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full group-hover:bg-jarthaz-gold group-hover:text-black group-hover:border-jarthaz-gold transition-colors duration-300"
                    asChild
                  >
                    <Link href="/packages/cultural-tours">View Package</Link>
                  </Button>
                </div>
              </motion.div>
            </div>
          </AnimatedSection>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-primary text-primary-foreground relative overflow-hidden">
        {/* Safari pattern overlay */}
        <div className="absolute inset-0 bg-[url('/placeholder.svg?height=200&width=200')] bg-repeat opacity-10 mix-blend-overlay" />

        {/* Decorative elements */}
        <div className="absolute top-10 right-10 w-32 h-32 border-4 border-jarthaz-gold/20 rounded-full"></div>
        <div className="absolute bottom-10 left-10 w-24 h-24 border-4 border-jarthaz-gold/10 rounded-full"></div>

        <div className="container text-center relative z-10">
          <AnimatedSection>
            <h2 className="text-3xl font-bold tracking-tight mb-4">Ready for an Adrenaline Rush?</h2>
            <p className="mb-8 max-w-2xl mx-auto text-primary-foreground/90">
              Contact us today to book your adventure activities in Uganda. Our team will help you plan the perfect
              experience.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                size="lg"
                variant="secondary"
                className="bg-jarthaz-gold hover:bg-jarthaz-gold/90 text-black border-jarthaz-gold"
                asChild
              >
                <Link href="/contact">Book Now</Link>
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="bg-transparent text-white border-white hover:bg-white hover:text-primary"
                asChild
              >
                <Link href="/destinations">Explore Other Packages</Link>
              </Button>
            </div>
          </AnimatedSection>
        </div>
      </section>
    </div>
  )
}
