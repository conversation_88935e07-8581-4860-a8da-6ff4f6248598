"use client"

import Image from "next/image"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Check, ArrowLeft, Users, Clock, MapPin, Star } from "lucide-react"
import AnimatedSection from "@/components/animated-section"
import { motion } from "framer-motion"

export default function WildlifeSafarisPage() {
  // Testimonials data
  const testimonials = [
    {
      name: "<PERSON>",
      location: "United Kingdom",
      text: "Our safari experience with Jarthaz Tours was exceptional. We saw all the Big Five and our guide was incredibly knowledgeable about the wildlife and ecosystems.",
      rating: 5,
      image: "/placeholder.svg?height=100&width=100",
    },
    {
      name: "<PERSON>",
      location: "United States",
      text: "The boat cruise on the Kazinga Channel was a highlight of our trip. We saw hundreds of hippos, elephants, and birds. Truly unforgettable!",
      rating: 5,
      image: "/placeholder.svg?height=100&width=100",
    },
    {
      name: "<PERSON>",
      location: "Kenya",
      text: "Even as someone from East Africa, I was amazed by Uganda's wildlife diversity. The tree-climbing lions in Queen Elizabeth National Park were a special treat.",
      rating: 4,
      image: "/placeholder.svg?height=100&width=100",
    },
  ]

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative h-[80vh] flex items-center overflow-hidden">
        <div className="absolute inset-0 z-0">
          <Image
            src="/images/wildlife/queen-elizabeth-landscape.jpeg"
            alt="Wildlife Safari in Queen Elizabeth National Park"
            fill
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-black/30"></div>
        </div>

        {/* Decorative elements */}
        <div className="absolute top-20 right-20 w-40 h-40 border-r-2 border-t-2 border-jarthaz-gold/30 z-10"></div>
        <div className="absolute bottom-20 left-20 w-40 h-40 border-l-2 border-b-2 border-jarthaz-gold/30 z-10"></div>

        <div className="container relative z-10 text-white">
          <motion.div
            className="max-w-3xl space-y-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <Link href="/destinations" className="inline-flex items-center text-white/90 hover:text-white mb-4 group">
              <ArrowLeft size={16} className="mr-2 group-hover:-translate-x-1 transition-transform" />
              Back to Destinations
            </Link>

            <div className="inline-block bg-jarthaz-gold/20 backdrop-blur-sm px-4 py-1 rounded-full text-white text-sm font-medium">
              Safari Experience
            </div>

            <h1 className="text-4xl font-bold tracking-tighter sm:text-5xl md:text-6xl text-shadow">
              Wildlife <span className="text-jarthaz-gold">Safaris</span>
            </h1>

            <p className="text-xl text-white/90 max-w-2xl">
              Experience the thrill of Uganda's wildlife in its natural splendor across our diverse national parks.
            </p>

            {/* Package highlights */}
            <div className="flex flex-wrap gap-4 mt-4">
              <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm px-3 py-1.5 rounded-full">
                <Clock size={16} className="text-jarthaz-gold" />
                <span className="text-sm">3-7 days</span>
              </div>
              <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm px-3 py-1.5 rounded-full">
                <Users size={16} className="text-jarthaz-gold" />
                <span className="text-sm">Small groups (2-8)</span>
              </div>
              <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm px-3 py-1.5 rounded-full">
                <MapPin size={16} className="text-jarthaz-gold" />
                <span className="text-sm">Multiple National Parks</span>
              </div>
              <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm px-3 py-1.5 rounded-full">
                <Star size={16} className="text-jarthaz-gold" />
                <span className="text-sm">4.9 (87 reviews)</span>
              </div>
            </div>

            <div className="pt-6">
              <Button size="lg" variant="safari" className="btn-shimmer" asChild>
                <Link href="#booking">Book This Experience</Link>
              </Button>
            </div>
          </motion.div>
        </div>

        <div className="absolute bottom-0 left-0 right-0">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320" className="text-background fill-current">
            <path d="M0,224L48,213.3C96,203,192,181,288,181.3C384,181,480,203,576,202.7C672,203,768,181,864,181.3C960,181,1056,203,1152,208C1248,213,1344,203,1392,197.3L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path>
          </svg>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-20">
        <div className="container">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {/* Left Content */}
            <div className="lg:col-span-2 space-y-12">
              <AnimatedSection>
                <div className="bg-white dark:bg-jarthaz-black/20 rounded-lg p-8 shadow-md">
                  <div className="inline-block bg-jarthaz-gold/10 px-4 py-1 rounded-full text-jarthaz-gold text-sm font-medium mb-4">
                    About The Experience
                  </div>
                  <h2 className="section-heading text-3xl">Wildlife Safaris</h2>
                  <div className="safari-divider w-24 my-6"></div>
                  <p className="text-muted-foreground mb-4">
                    Uganda offers some of Africa's most diverse wildlife viewing opportunities. From the tree-climbing
                    lions of Queen Elizabeth National Park to the massive elephant herds of Murchison Falls, our
                    wildlife safaris showcase the incredible biodiversity that makes Uganda a unique safari destination.
                  </p>
                  <p className="text-muted-foreground mb-4">
                    Unlike the open savannahs of Kenya and Tanzania, Uganda's varied landscapes provide a different kind
                    of safari experience. Here, you'll find lush forests, expansive lakes, winding rivers, and rolling
                    hills that create diverse habitats for an impressive array of wildlife.
                  </p>
                  <p className="text-muted-foreground">
                    Our expertly guided safaris take you to Uganda's premier national parks, where you'll have the
                    opportunity to spot the Big Five and numerous other species in stunning natural settings.
                  </p>
                </div>
              </AnimatedSection>

              <AnimatedSection delay={0.2}>
                <div className="relative h-[500px] rounded-lg overflow-hidden shadow-xl">
                  <Image
                    src="/images/wildlife/elephant-family-savanna.jpeg"
                    alt="Elephant family in Uganda's savanna"
                    fill
                    className="object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-jarthaz-black/50 to-transparent"></div>

                  {/* Image caption */}
                  <div className="absolute bottom-0 left-0 right-0 p-6 bg-jarthaz-black/60 backdrop-blur-sm">
                    <h3 className="text-white font-medium text-lg">Elephant Family in Uganda's Savanna</h3>
                    <p className="text-white/80 text-sm">
                      Uganda's national parks are home to thousands of elephants roaming freely in their natural habitat
                    </p>
                  </div>
                </div>
              </AnimatedSection>

              <AnimatedSection delay={0.3}>
                <div className="bg-white dark:bg-jarthaz-black/20 rounded-lg p-8 shadow-md">
                  <div className="inline-block bg-jarthaz-gold/10 px-4 py-1 rounded-full text-jarthaz-gold text-sm font-medium mb-4">
                    Safari Destinations
                  </div>
                  <h2 className="section-heading text-3xl">Our Safari Destinations</h2>
                  <div className="safari-divider w-24 my-6"></div>

                  <div className="space-y-8">
                    <div className="flex flex-col md:flex-row gap-6 items-start">
                      <div className="md:w-1/3 relative h-60 rounded-lg overflow-hidden shadow-md flex-shrink-0">
                        <Image
                          src="/images/wildlife/queen-elizabeth-entrance.jpeg"
                          alt="Queen Elizabeth National Park entrance"
                          fill
                          className="object-cover"
                        />
                      </div>
                      <div className="flex-1">
                        <h3 className="text-xl font-bold mb-3">Queen Elizabeth National Park</h3>
                        <p className="text-muted-foreground mb-3">
                          Uganda's most popular safari destination, known for its diverse ecosystems, including
                          savannah, wetlands, and forests. The park is home to over 95 mammal species and 600 bird
                          species.
                        </p>
                        <p className="text-muted-foreground mb-2">
                          <span className="font-medium">Key Wildlife:</span> Tree-climbing lions in the Ishasha sector,
                          elephants, hippos, buffaloes, leopards, and a variety of antelope species.
                        </p>
                        <p className="text-muted-foreground">
                          <span className="font-medium">Highlights:</span> Game drives, boat cruises on the Kazinga
                          Channel, chimpanzee tracking in Kyambura Gorge, and bird watching.
                        </p>
                      </div>
                    </div>

                    <div className="flex flex-col md:flex-row gap-6 items-start">
                      <div className="md:w-1/3 relative h-60 rounded-lg overflow-hidden shadow-md flex-shrink-0">
                        <Image
                          src="/images/wildlife/murchison-falls-aerial.jpeg"
                          alt="Aerial view of Murchison Falls"
                          fill
                          className="object-cover"
                        />
                      </div>
                      <div className="flex-1">
                        <h3 className="text-xl font-bold mb-3">Murchison Falls National Park</h3>
                        <p className="text-muted-foreground mb-3">
                          Uganda's largest national park, bisected by the Victoria Nile. The park is named after the
                          dramatic Murchison Falls, where the Nile squeezes through a narrow gorge before plunging 43
                          meters.
                        </p>
                        <p className="text-muted-foreground mb-2">
                          <span className="font-medium">Key Wildlife:</span> Elephants, giraffes, buffaloes, hippos,
                          Nile crocodiles, lions, and over 450 bird species.
                        </p>
                        <p className="text-muted-foreground">
                          <span className="font-medium">Highlights:</span> Game drives, boat safaris to the base of the
                          falls, hiking to the top of the falls, and sport fishing.
                        </p>
                      </div>
                    </div>

                    <div className="flex flex-col md:flex-row gap-6 items-start">
                      <div className="md:w-1/3 relative h-60 rounded-lg overflow-hidden shadow-md flex-shrink-0">
                        <Image
                          src="/images/wildlife/zebra-pair-closeup.jpeg"
                          alt="Zebras in Lake Mburo National Park"
                          fill
                          className="object-cover"
                        />
                      </div>
                      <div className="flex-1">
                        <h3 className="text-xl font-bold mb-3">Lake Mburo National Park</h3>
                        <p className="text-muted-foreground mb-3">
                          A compact gem located conveniently close to the main highway connecting Kampala to western
                          Uganda. The park is centered around Lake Mburo and contains several other lakes.
                        </p>
                        <p className="text-muted-foreground mb-2">
                          <span className="font-medium">Key Wildlife:</span> Zebras, impalas, elands, topis, buffaloes,
                          and over 350 bird species.
                        </p>
                        <p className="text-muted-foreground">
                          <span className="font-medium">Highlights:</span> Game drives, boat trips on Lake Mburo, guided
                          walking safaris, horseback safaris, and mountain biking.
                        </p>
                      </div>
                    </div>

                    <div className="bg-muted/50 p-6 rounded-lg">
                      <h3 className="font-bold text-xl mb-3">Kidepo Valley National Park</h3>
                      <p className="text-muted-foreground mb-3">
                        A remote wilderness in northeastern Uganda, offering a true off-the-beaten-path safari
                        experience. The park features rugged savannah dominated by the Mount Morungole range.
                      </p>
                      <p className="text-muted-foreground mb-2">
                        <span className="font-medium">Key Wildlife:</span> Zebras, giraffes, ostriches, cheetahs, lions,
                        elephants, and over 77 mammal species not found in any other Ugandan park.
                      </p>
                      <p className="text-muted-foreground">
                        <span className="font-medium">Highlights:</span> Game drives in Narus Valley, cultural visits to
                        Karamojong villages, and visits to the Kanangorok Hot Springs.
                      </p>
                    </div>
                  </div>
                </div>
              </AnimatedSection>

              <AnimatedSection delay={0.4}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="relative h-[300px] rounded-lg overflow-hidden shadow-md">
                    <Image
                      src="/images/wildlife/hippos-water.png"
                      alt="Hippos in Lake Mburo"
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-jarthaz-black/70 to-transparent"></div>
                    <div className="absolute bottom-0 left-0 right-0 p-4">
                      <p className="text-white text-sm">Hippos cooling off in Uganda's waters</p>
                    </div>
                  </div>
                  <div className="relative h-[300px] rounded-lg overflow-hidden shadow-md">
                    <Image
                      src="/images/wildlife/murchison-falls-dramatic.jpeg"
                      alt="The spectacular Murchison Falls"
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-jarthaz-black/70 to-transparent"></div>
                    <div className="absolute bottom-0 left-0 right-0 p-4">
                      <p className="text-white text-sm">The spectacular Murchison Falls on the Victoria Nile</p>
                    </div>
                  </div>
                </div>
              </AnimatedSection>

              <AnimatedSection delay={0.5}>
                <div className="bg-white dark:bg-jarthaz-black/20 rounded-lg p-8 shadow-md">
                  <div className="inline-block bg-jarthaz-gold/10 px-4 py-1 rounded-full text-jarthaz-gold text-sm font-medium mb-4">
                    Sample Itinerary
                  </div>
                  <h2 className="section-heading text-3xl">Featured Itineraries</h2>
                  <div className="safari-divider w-24 my-6"></div>

                  <div className="space-y-8 mt-6">
                    <div>
                      <h3 className="font-bold text-xl mb-4">3-Day Queen Elizabeth National Park Safari</h3>
                      <div className="space-y-4">
                        <div className="relative pl-8 pb-8 border-l-2 border-jarthaz-gold/30">
                          <div className="absolute top-0 left-0 w-8 h-8 -translate-x-1/2 rounded-full bg-jarthaz-gold flex items-center justify-center">
                            <span className="text-jarthaz-black font-bold">1</span>
                          </div>
                          <h4 className="font-bold text-lg mb-3">Day 1: Kampala to Queen Elizabeth National Park</h4>
                          <div className="bg-muted/30 p-6 rounded-lg">
                            <p className="text-muted-foreground">
                              Early morning departure from Kampala. Drive west through scenic landscapes and local
                              villages. Stop at the equator for photos and demonstrations. Continue to Queen Elizabeth
                              National Park, arriving in time for an evening game drive or relaxation at your lodge.
                              Dinner and overnight at a safari lodge within or near the park.
                            </p>
                            <div className="flex flex-wrap gap-4 mt-4 text-sm">
                              <div className="flex items-center gap-1">
                                <Clock size={14} className="text-jarthaz-gold" />
                                <span className="font-medium">Duration:</span> 6-7 hours drive
                              </div>
                              <div className="flex items-center gap-1">
                                <MapPin size={14} className="text-jarthaz-gold" />
                                <span className="font-medium">Accommodation:</span> Safari Lodge
                              </div>
                              <div className="flex items-center gap-1">
                                <Users size={14} className="text-jarthaz-gold" />
                                <span className="font-medium">Meals:</span> Lunch, Dinner
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className="relative pl-8 pb-8 border-l-2 border-jarthaz-gold/30">
                          <div className="absolute top-0 left-0 w-8 h-8 -translate-x-1/2 rounded-full bg-jarthaz-gold flex items-center justify-center">
                            <span className="text-jarthaz-black font-bold">2</span>
                          </div>
                          <h4 className="font-bold text-lg mb-3">Day 2: Game Drive and Boat Cruise</h4>
                          <div className="bg-muted/30 p-6 rounded-lg">
                            <p className="text-muted-foreground">
                              Early morning game drive in search of lions, leopards, elephants, and various antelope
                              species. Return to the lodge for lunch. Afternoon boat cruise along the Kazinga Channel to
                              observe hippos, crocodiles, and water birds up close. Optional visit to the local Katwe
                              salt mines. Dinner and overnight at your lodge.
                            </p>
                            <div className="flex flex-wrap gap-4 mt-4 text-sm">
                              <div className="flex items-center gap-1">
                                <Clock size={14} className="text-jarthaz-gold" />
                                <span className="font-medium">Activities:</span> Game drive (3-4 hours), Boat cruise (2
                                hours)
                              </div>
                              <div className="flex items-center gap-1">
                                <MapPin size={14} className="text-jarthaz-gold" />
                                <span className="font-medium">Accommodation:</span> Same lodge
                              </div>
                              <div className="flex items-center gap-1">
                                <Users size={14} className="text-jarthaz-gold" />
                                <span className="font-medium">Meals:</span> Breakfast, Lunch, Dinner
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className="relative pl-8">
                          <div className="absolute top-0 left-0 w-8 h-8 -translate-x-1/2 rounded-full bg-jarthaz-gold flex items-center justify-center">
                            <span className="text-jarthaz-black font-bold">3</span>
                          </div>
                          <h4 className="font-bold text-lg mb-3">Day 3: Chimpanzee Tracking and Return to Kampala</h4>
                          <div className="bg-muted/30 p-6 rounded-lg">
                            <p className="text-muted-foreground">
                              Optional morning chimpanzee tracking in Kyambura Gorge (additional permit required). After
                              breakfast, begin the journey back to Kampala with a stop at the equator for lunch and
                              shopping. Arrive in Kampala in the evening.
                            </p>
                            <div className="flex flex-wrap gap-4 mt-4 text-sm">
                              <div className="flex items-center gap-1">
                                <Clock size={14} className="text-jarthaz-gold" />
                                <span className="font-medium">Duration:</span> 6-7 hours drive
                              </div>
                              <div className="flex items-center gap-1">
                                <Users size={14} className="text-jarthaz-gold" />
                                <span className="font-medium">Meals:</span> Breakfast, Lunch
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h3 className="font-bold text-xl mb-4">4-Day Murchison Falls National Park Safari</h3>
                      <div className="space-y-4">
                        <div className="relative pl-8 pb-8 border-l-2 border-jarthaz-gold/30">
                          <div className="absolute top-0 left-0 w-8 h-8 -translate-x-1/2 rounded-full bg-jarthaz-gold flex items-center justify-center">
                            <span className="text-jarthaz-black font-bold">1</span>
                          </div>
                          <h4 className="font-bold text-lg mb-3">Day 1: Kampala to Murchison Falls</h4>
                          <div className="bg-muted/30 p-6 rounded-lg">
                            <p className="text-muted-foreground">
                              Depart from Kampala to Murchison Falls National Park. En route, visit Ziwa Rhino Sanctuary
                              for rhino tracking. Continue to the park and check in at your lodge. Evening relaxation
                              with views of the Nile or surrounding wilderness.
                            </p>
                            <div className="flex flex-wrap gap-4 mt-4 text-sm">
                              <div className="flex items-center gap-1">
                                <Clock size={14} className="text-jarthaz-gold" />
                                <span className="font-medium">Duration:</span> 5-6 hours drive
                              </div>
                              <div className="flex items-center gap-1">
                                <MapPin size={14} className="text-jarthaz-gold" />
                                <span className="font-medium">Accommodation:</span> Safari Lodge
                              </div>
                              <div className="flex items-center gap-1">
                                <Users size={14} className="text-jarthaz-gold" />
                                <span className="font-medium">Meals:</span> Lunch, Dinner
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className="relative pl-8 pb-8 border-l-2 border-jarthaz-gold/30">
                          <div className="absolute top-0 left-0 w-8 h-8 -translate-x-1/2 rounded-full bg-jarthaz-gold flex items-center justify-center">
                            <span className="text-jarthaz-black font-bold">2</span>
                          </div>
                          <h4 className="font-bold text-lg mb-3">Day 2: Game Drive and Boat Cruise</h4>
                          <div className="bg-muted/30 p-6 rounded-lg">
                            <p className="text-muted-foreground">
                              Early morning game drive on the northern bank of the Nile to spot giraffes, elephants,
                              lions, and more. Return to the lodge for lunch. Afternoon boat cruise to the base of
                              Murchison Falls, offering close views of hippos, crocodiles, and water birds. Optional
                              hike to the top of the falls.
                            </p>
                            <div className="flex flex-wrap gap-4 mt-4 text-sm">
                              <div className="flex items-center gap-1">
                                <Clock size={14} className="text-jarthaz-gold" />
                                <span className="font-medium">Activities:</span> Game drive (3-4 hours), Boat cruise (3
                                hours)
                              </div>
                              <div className="flex items-center gap-1">
                                <MapPin size={14} className="text-jarthaz-gold" />
                                <span className="font-medium">Accommodation:</span> Same lodge
                              </div>
                              <div className="flex items-center gap-1">
                                <Users size={14} className="text-jarthaz-gold" />
                                <span className="font-medium">Meals:</span> Breakfast, Lunch, Dinner
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className="relative pl-8 pb-8 border-l-2 border-jarthaz-gold/30">
                          <div className="absolute top-0 left-0 w-8 h-8 -translate-x-1/2 rounded-full bg-jarthaz-gold flex items-center justify-center">
                            <span className="text-jarthaz-black font-bold">3</span>
                          </div>
                          <h4 className="font-bold text-lg mb-3">Day 3: Delta Cruise and Nature Walk</h4>
                          <div className="bg-muted/30 p-6 rounded-lg">
                            <p className="text-muted-foreground">
                              Morning boat cruise to the Nile Delta, where the river enters Lake Albert. This area is
                              excellent for bird watching, including the rare shoebill stork. Afternoon nature walk or
                              game drive. Evening relaxation at the lodge.
                            </p>
                            <div className="flex flex-wrap gap-4 mt-4 text-sm">
                              <div className="flex items-center gap-1">
                                <Clock size={14} className="text-jarthaz-gold" />
                                <span className="font-medium">Activities:</span> Delta cruise (3 hours), Nature walk (2
                                hours)
                              </div>
                              <div className="flex items-center gap-1">
                                <MapPin size={14} className="text-jarthaz-gold" />
                                <span className="font-medium">Accommodation:</span> Same lodge
                              </div>
                              <div className="flex items-center gap-1">
                                <Users size={14} className="text-jarthaz-gold" />
                                <span className="font-medium">Meals:</span> Breakfast, Lunch, Dinner
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className="relative pl-8">
                          <div className="absolute top-0 left-0 w-8 h-8 -translate-x-1/2 rounded-full bg-jarthaz-gold flex items-center justify-center">
                            <span className="text-jarthaz-black font-bold">4</span>
                          </div>
                          <h4 className="font-bold text-lg mb-3">Day 4: Return to Kampala</h4>
                          <div className="bg-muted/30 p-6 rounded-lg">
                            <p className="text-muted-foreground">
                              After breakfast, begin the journey back to Kampala. Stop for lunch and souvenir shopping
                              along the way. Arrive in Kampala in the late afternoon.
                            </p>
                            <div className="flex flex-wrap gap-4 mt-4 text-sm">
                              <div className="flex items-center gap-1">
                                <Clock size={14} className="text-jarthaz-gold" />
                                <span className="font-medium">Duration:</span> 5-6 hours drive
                              </div>
                              <div className="flex items-center gap-1">
                                <Users size={14} className="text-jarthaz-gold" />
                                <span className="font-medium">Meals:</span> Breakfast, Lunch
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </AnimatedSection>

              <AnimatedSection delay={0.6}>
                <div className="relative h-[500px] rounded-lg overflow-hidden shadow-xl">
                  <Image
                    src="/images/wildlife/giraffe-herd-path.jpeg"
                    alt="Giraffes on safari in Uganda"
                    fill
                    className="object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-jarthaz-black/50 to-transparent"></div>

                  {/* Image caption */}
                  <div className="absolute bottom-0 left-0 right-0 p-6 bg-jarthaz-black/60 backdrop-blur-sm">
                    <h3 className="text-white font-medium text-lg">Giraffes in Their Natural Habitat</h3>
                    <p className="text-white/80 text-sm">
                      Encounter these majestic creatures up close during game drives in Uganda's national parks
                    </p>
                  </div>
                </div>
              </AnimatedSection>

              <AnimatedSection delay={0.7}>
                <div className="bg-white dark:bg-jarthaz-black/20 rounded-lg p-8 shadow-md">
                  <div className="inline-block bg-jarthaz-gold/10 px-4 py-1 rounded-full text-jarthaz-gold text-sm font-medium mb-4">
                    Safari Experience
                  </div>
                  <h2 className="section-heading text-3xl">What to Expect</h2>
                  <div className="safari-divider w-24 my-6"></div>

                  <div className="space-y-6">
                    <div>
                      <h3 className="font-semibold text-lg flex items-center gap-2">
                        <div className="w-6 h-6 rounded-full bg-jarthaz-gold/20 flex items-center justify-center flex-shrink-0">
                          <Users size={14} className="text-jarthaz-gold" />
                        </div>
                        Game Drives
                      </h3>
                      <p className="text-muted-foreground mt-2">
                        Our game drives are conducted in comfortable 4x4 safari vehicles with pop-up roofs, ensuring
                        excellent wildlife viewing and photography opportunities. Experienced guides will help you spot
                        and identify animals while sharing fascinating insights about their behavior and the ecosystem.
                      </p>
                    </div>

                    <div>
                      <h3 className="font-semibold text-lg flex items-center gap-2">
                        <div className="w-6 h-6 rounded-full bg-jarthaz-gold/20 flex items-center justify-center flex-shrink-0">
                          <Check size={14} className="text-jarthaz-gold" />
                        </div>
                        Boat Safaris
                      </h3>
                      <p className="text-muted-foreground mt-2">
                        Boat cruises on the Kazinga Channel, Lake Mburo, and the Nile River offer a different
                        perspective on wildlife. These water-based safaris bring you close to hippos, crocodiles, and
                        numerous water birds, as well as animals that come to the water's edge to drink.
                      </p>
                    </div>

                    <div>
                      <h3 className="font-semibold text-lg flex items-center gap-2">
                        <div className="w-6 h-6 rounded-full bg-jarthaz-gold/20 flex items-center justify-center flex-shrink-0">
                          <Check size={14} className="text-jarthaz-gold" />
                        </div>
                        Walking Safaris
                      </h3>
                      <p className="text-muted-foreground mt-2">
                        In select areas, guided walking safaris allow you to experience the African bush on foot. These
                        walks focus on smaller wildlife, plant life, and tracking skills, offering a more intimate
                        connection with nature.
                      </p>
                    </div>

                    <div>
                      <h3 className="font-semibold text-lg flex items-center gap-2">
                        <div className="w-6 h-6 rounded-full bg-jarthaz-gold/20 flex items-center justify-center flex-shrink-0">
                          <Check size={14} className="text-jarthaz-gold" />
                        </div>
                        Accommodation
                      </h3>
                      <p className="text-muted-foreground mt-2">
                        We offer a range of accommodation options to suit different preferences and budgets, from luxury
                        lodges to mid-range and budget-friendly options. All provide comfortable beds, private
                        bathrooms, and delicious meals, often with stunning views of the surrounding wilderness.
                      </p>
                    </div>
                  </div>
                </div>
              </AnimatedSection>

              {/* Testimonials */}
              <AnimatedSection delay={0.8}>
                <div className="bg-white dark:bg-jarthaz-black/20 rounded-lg p-8 shadow-md">
                  <div className="inline-block bg-jarthaz-gold/10 px-4 py-1 rounded-full text-jarthaz-gold text-sm font-medium mb-4">
                    Guest Experiences
                  </div>
                  <h2 className="section-heading text-3xl">Testimonials</h2>
                  <div className="safari-divider w-24 my-6"></div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {testimonials.map((testimonial, index) => (
                      <div key={index} className="bg-muted/30 p-6 rounded-lg">
                        <div className="flex items-start gap-4">
                          <div className="relative w-12 h-12 rounded-full overflow-hidden">
                            <Image
                              src={testimonial.image || "/placeholder.svg"}
                              alt={testimonial.name}
                              fill
                              className="object-cover"
                            />
                          </div>
                          <div>
                            <h4 className="font-bold">{testimonial.name}</h4>
                            <p className="text-sm text-muted-foreground">{testimonial.location}</p>
                            <div className="flex items-center gap-1 mt-1">
                              {[...Array(5)].map((_, i) => (
                                <Star
                                  key={i}
                                  size={14}
                                  className={
                                    i < testimonial.rating ? "text-jarthaz-gold fill-jarthaz-gold" : "text-muted"
                                  }
                                />
                              ))}
                            </div>
                          </div>
                        </div>
                        <p className="mt-4 text-muted-foreground italic">"{testimonial.text}"</p>
                      </div>
                    ))}
                  </div>
                </div>
              </AnimatedSection>
            </div>

            {/* Right Sidebar */}
            <div className="space-y-8">
              <AnimatedSection direction="left">
                <div id="booking" className="bg-white dark:bg-jarthaz-black/20 rounded-lg p-6 shadow-md sticky top-24">
                  <h3 className="text-xl font-bold mb-4 text-jarthaz-gold">Book Your Safari</h3>
                  <div className="safari-divider w-16 my-4"></div>

                  <div className="space-y-4 mb-6">
                    <div className="flex justify-between items-center pb-2 border-b border-border">
                      <span className="text-sm">Starting Price</span>

                    </div>
                    <div className="flex justify-between items-center pb-2 border-b border-border">
                      <span className="text-sm">Duration</span>
                      <span>3-7 days</span>
                    </div>
                    <div className="flex justify-between items-center pb-2 border-b border-border">
                      <span className="text-sm">Group Size</span>
                      <span>2-8 people</span>
                    </div>
                    <div className="flex justify-between items-center pb-2 border-b border-border">
                      <span className="text-sm">Difficulty</span>
                      <span>Easy</span>
                    </div>
                    <div className="flex justify-between items-center pb-2 border-b border-border">
                      <span className="text-sm">Park Fees</span>
                      <span>Included</span>
                    </div>
                  </div>

                  <p className="mb-6 text-sm text-muted-foreground">
                    Contact us to customize your perfect wildlife safari in Uganda's magnificent national parks.
                  </p>

                  <Button variant="safari" size="lg" className="w-full btn-shimmer" asChild>
                    <Link href="/contact">Contact Us to Book</Link>
                  </Button>
                </div>
              </AnimatedSection>

              <AnimatedSection direction="left" delay={0.2}>
                <div className="bg-white dark:bg-jarthaz-black/20 rounded-lg p-6 shadow-md">
                  <h3 className="text-xl font-bold mb-4 text-jarthaz-gold">Safari Highlights</h3>
                  <div className="safari-divider w-16 my-4"></div>

                  <ul className="space-y-3">
                    <li className="flex items-start gap-3">
                      <div className="w-6 h-6 rounded-full bg-jarthaz-gold/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                        <Check size={14} className="text-jarthaz-gold" />
                      </div>
                      <span className="text-sm">Diverse wildlife viewing in multiple national parks</span>
                    </li>
                    <li className="flex items-start gap-3">
                      <div className="w-6 h-6 rounded-full bg-jarthaz-gold/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                        <Check size={14} className="text-jarthaz-gold" />
                      </div>
                      <span className="text-sm">Comfortable 4x4 safari vehicles with pop-up roofs</span>
                    </li>
                    <li className="flex items-start gap-3">
                      <div className="w-6 h-6 rounded-full bg-jarthaz-gold/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                        <Check size={14} className="text-jarthaz-gold" />
                      </div>
                      <span className="text-sm">Experienced English-speaking guides</span>
                    </li>
                    <li className="flex items-start gap-3">
                      <div className="w-6 h-6 rounded-full bg-jarthaz-gold/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                        <Check size={14} className="text-jarthaz-gold" />
                      </div>
                      <span className="text-sm">Boat cruises for water-based wildlife viewing</span>
                    </li>
                    <li className="flex items-start gap-3">
                      <div className="w-6 h-6 rounded-full bg-jarthaz-gold/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                        <Check size={14} className="text-jarthaz-gold" />
                      </div>
                      <span className="text-sm">Quality accommodation in scenic locations</span>
                    </li>
                  </ul>
                </div>
              </AnimatedSection>

              <AnimatedSection direction="left" delay={0.3}>
                <div className="bg-white dark:bg-jarthaz-black/20 rounded-lg p-6 shadow-md">
                  <h3 className="text-xl font-bold mb-4 text-jarthaz-gold">Best Time to Visit</h3>
                  <div className="safari-divider w-16 my-4"></div>

                  <p className="text-sm text-muted-foreground">
                    Wildlife viewing is good year-round, but the best times are during the dry seasons:
                  </p>

                  <div className="space-y-3 mb-4 mt-3">
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 rounded-full bg-jarthaz-gold/20 flex items-center justify-center">
                        <div className="w-2 h-2 rounded-full bg-jarthaz-gold"></div>
                      </div>
                      <span>December to February</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 rounded-full bg-jarthaz-gold/20 flex items-center justify-center">
                        <div className="w-2 h-2 rounded-full bg-jarthaz-gold"></div>
                      </div>
                      <span>June to September</span>
                    </div>
                  </div>

                  <p className="text-sm text-muted-foreground">
                    During these periods, animals gather around water sources, making them easier to spot. The
                    vegetation is also less dense, improving visibility.
                  </p>
                </div>
              </AnimatedSection>

              <AnimatedSection direction="left" delay={0.4}>
                <div className="bg-white dark:bg-jarthaz-black/20 rounded-lg overflow-hidden shadow-md">
                  <div className="relative h-48">
                    <Image
                      src="/images/wildlife/crater-lake.jpeg"
                      alt="Crater lake in Queen Elizabeth National Park"
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-jarthaz-black/70 to-transparent"></div>
                    <div className="absolute bottom-0 left-0 w-full p-4 text-white">
                      <h3 className="font-semibold text-lg">Conservation Note</h3>
                    </div>
                  </div>
                  <div className="p-4">
                    <p className="text-sm text-muted-foreground">
                      A portion of your safari fee goes directly to conservation efforts and community development
                      projects around the national parks, helping to ensure the protection of Uganda's wildlife and
                      natural habitats for generations to come.
                    </p>
                    <div className="mt-4 pt-4 border-t border-border">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Conservation Contribution</span>

                      </div>
                    </div>
                  </div>
                </div>
              </AnimatedSection>

              <AnimatedSection direction="left" delay={0.5}>
                <div className="bg-white dark:bg-jarthaz-black/20 rounded-lg overflow-hidden shadow-md">
                  <div className="relative h-48">
                    <Image
                      src="/images/wildlife/horseback-zebra-safari.jpeg"
                      alt="Horseback safari with zebras"
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-jarthaz-black/70 to-transparent"></div>
                    <div className="absolute bottom-0 left-0 w-full p-4 text-white">
                      <h3 className="font-semibold text-lg">Unique Experiences</h3>
                    </div>
                  </div>
                  <div className="p-4">
                    <p className="text-sm text-muted-foreground mb-3">
                      Enhance your safari with these unique experiences available in select parks:
                    </p>
                    <ul className="space-y-2 text-sm text-muted-foreground">
                      <li className="flex items-start gap-2">
                        <div className="w-4 h-4 rounded-full bg-jarthaz-gold/20 flex items-center justify-center mt-0.5">
                          <div className="w-2 h-2 rounded-full bg-jarthaz-gold"></div>
                        </div>
                        <span>Horseback safaris in Lake Mburo National Park</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <div className="w-4 h-4 rounded-full bg-jarthaz-gold/20 flex items-center justify-center mt-0.5">
                          <div className="w-2 h-2 rounded-full bg-jarthaz-gold"></div>
                        </div>
                        <span>Hot air balloon safaris over Queen Elizabeth National Park</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <div className="w-4 h-4 rounded-full bg-jarthaz-gold/20 flex items-center justify-center mt-0.5">
                          <div className="w-2 h-2 rounded-full bg-jarthaz-gold"></div>
                        </div>
                        <span>Night game drives in select areas</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </AnimatedSection>
            </div>
          </div>
        </div>
      </section>

      {/* Related Packages */}
      <section className="py-20 safari-pattern-bg relative overflow-hidden">
        {/* Decorative elements */}
        <div className="absolute top-0 right-0 w-64 h-64 bg-jarthaz-gold/5 rounded-full -translate-y-1/2 translate-x-1/2"></div>
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-jarthaz-gold/5 rounded-full translate-y-1/2 -translate-x-1/2"></div>

        <div className="container relative">
          <AnimatedSection>
            <div className="flex flex-col items-center text-center mb-12">
              <div className="inline-block bg-jarthaz-gold/10 px-4 py-1 rounded-full text-jarthaz-gold text-sm font-medium mb-4">
                More Adventures
              </div>
              <h2 className="section-heading text-4xl">You Might Also Like</h2>
              <div className="safari-divider w-24 my-6"></div>
            </div>
          </AnimatedSection>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <AnimatedSection delay={0.2}>
              <div className="bg-white dark:bg-jarthaz-black/50 rounded-lg overflow-hidden shadow-md card-hover h-full flex flex-col">
                <div className="relative h-48 image-hover-zoom">
                  <Image
                    src="/images/gorillas/gorilla-family.jpeg"
                    alt="Gorilla Trekking"
                    fill
                    className="object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
                </div>

                <div className="p-5 flex-grow flex flex-col">
                  <h3 className="font-bold text-lg mb-2">Gorilla Trekking</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Face-to-face encounters with mountain gorillas in Bwindi Impenetrable Forest.
                  </p>

                  {/* Package details */}
                  <div className="flex flex-wrap gap-3 mb-4 mt-auto">
                    <div className="flex items-center text-xs text-muted-foreground">
                      <Clock size={14} className="mr-1 text-jarthaz-gold" />
                      <span>3-5 days</span>
                    </div>
                    <div className="flex items-center text-xs text-muted-foreground">
                      <Users size={14} className="mr-1 text-jarthaz-gold" />
                      <span>2-8 people</span>
                    </div>
                  </div>

                  <Button variant="safari" size="sm" className="w-full" asChild>
                    <Link href="/packages/gorilla-trekking" target="_blank">
                      View Package
                    </Link>
                  </Button>
                </div>
              </div>
            </AnimatedSection>

            <AnimatedSection delay={0.3}>
              <div className="bg-white dark:bg-jarthaz-black/50 rounded-lg overflow-hidden shadow-md card-hover h-full flex flex-col">
                <div className="relative h-48 image-hover-zoom">
                  <Image
                    src="/images/adventures/rafting-nile.jpeg"
                    alt="Adventure Escapades"
                    fill
                    className="object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
                </div>

                <div className="p-5 flex-grow flex flex-col">
                  <h3 className="font-bold text-lg mb-2">Adventure Escapades</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Thrilling activities including white-water rafting and bungee jumping.
                  </p>

                  {/* Package details */}
                  <div className="flex flex-wrap gap-3 mb-4 mt-auto">
                    <div className="flex items-center text-xs text-muted-foreground">
                      <Clock size={14} className="mr-1 text-jarthaz-gold" />
                      <span>1-3 days</span>
                    </div>
                    <div className="flex items-center text-xs text-muted-foreground">
                      <Users size={14} className="mr-1 text-jarthaz-gold" />
                      <span>1-10 people</span>
                    </div>
                  </div>

                  <Button variant="safari" size="sm" className="w-full" asChild>
                    <Link href="/packages/adventure-escapades" target="_blank">
                      View Package
                    </Link>
                  </Button>
                </div>
              </div>
            </AnimatedSection>

            <AnimatedSection delay={0.4}>
              <div className="bg-white dark:bg-jarthaz-black/50 rounded-lg overflow-hidden shadow-md card-hover h-full flex flex-col">
                <div className="relative h-48 image-hover-zoom">
                  <Image src="/images/cultural/ndere-dancers.webp" alt="Cultural Tours" fill className="object-cover" />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
                </div>

                <div className="p-5 flex-grow flex flex-col">
                  <h3 className="font-bold text-lg mb-2">Cultural Tours</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Immerse yourself in Uganda's rich cultural heritage and traditions.
                  </p>

                  {/* Package details */}
                  <div className="flex flex-wrap gap-3 mb-4 mt-auto">
                    <div className="flex items-center text-xs text-muted-foreground">
                      <Clock size={14} className="mr-1 text-jarthaz-gold" />
                      <span>2-5 days</span>
                    </div>
                    <div className="flex items-center text-xs text-muted-foreground">
                      <Users size={14} className="mr-1 text-jarthaz-gold" />
                      <span>2-15 people</span>
                    </div>
                  </div>

                  <Button variant="safari" size="sm" className="w-full" asChild>
                    <Link href="/packages/cultural-tours" target="_blank">
                      View Package
                    </Link>
                  </Button>
                </div>
              </div>
            </AnimatedSection>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-jarthaz-black text-white relative overflow-hidden">
        <div className="absolute inset-0 z-0">
          <Image
            src="/images/wildlife/elephant-family-savanna.jpeg"
            alt="Elephant herd in Uganda"
            fill
            className="object-cover opacity-30"
          />
        </div>

        {/* Decorative elements */}
        <div className="absolute top-0 left-0 w-full h-12 bg-gradient-to-b from-background to-transparent z-10"></div>
        <div className="absolute bottom-0 left-0 w-full h-12 bg-gradient-to-t from-background to-transparent z-10"></div>
        <div className="absolute top-12 right-12 w-24 h-24 border-r-2 border-t-2 border-jarthaz-gold/30 z-10"></div>
        <div className="absolute bottom-12 left-12 w-24 h-24 border-l-2 border-b-2 border-jarthaz-gold/30 z-10"></div>

        <div className="container text-center relative z-10">
          <AnimatedSection>
            <div className="inline-block bg-jarthaz-gold/20 backdrop-blur-sm px-4 py-1 rounded-full text-white text-sm font-medium mb-6">
              Book Your Safari
            </div>
            <h2 className="text-3xl md:text-4xl font-bold tracking-tight mb-4 text-jarthaz-gold">
              Ready for an Unforgettable Safari Adventure?
            </h2>
            <div className="safari-divider w-24 mx-auto my-6"></div>
            <p className="mb-8 max-w-2xl mx-auto text-white/90">
              Contact us today to book your wildlife safari in Uganda. Our team will help you plan the perfect
              itinerary.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" variant="safari" asChild className="btn-shimmer">
                <Link href="/contact">Book Now</Link>
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="bg-white/10 backdrop-blur-sm border-white/20 text-white hover:bg-white/20 hover:text-white"
                asChild
              >
                <Link href="/destinations">Explore Other Packages</Link>
              </Button>
            </div>
          </AnimatedSection>
        </div>
      </section>
    </div>
  )
}
