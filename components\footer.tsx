import Link from "next/link"
import Image from "next/image"
import { Facebook, Instagram, Twitter, Mail, Phone, MapPin } from "lucide-react"

export default function Footer() {
  const currentYear = new Date().getFullYear()

  return (
    <footer className="bg-jarthaz-black text-white">
      <div className="safari-silhouette">
        <div className="container py-12 md:py-16">
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Image
                  src="/images/logo.png"
                  alt="Jarthaz Tours Logo"
                  width={150}
                  height={50}
                  className="object-contain"
                />
              </div>
              <p className="text-sm text-gray-300 mb-4 max-w-xs">
                Your trusted partner in creating unforgettable travel experiences across the Pearl of Africa.
              </p>
              <div className="flex space-x-4">
                <Link
                  href="https://www.facebook.com/Jarthaztoursandtravel"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="hover:text-jarthaz-gold transition-colors"
                >
                  <Facebook size={20} />
                  <span className="sr-only">Facebook</span>
                </Link>
                <Link
                  href="https://x.com/JarthazTours"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="hover:text-jarthaz-gold transition-colors"
                >
                  <Twitter size={20} />
                  <span className="sr-only">Twitter</span>
                </Link>
                <Link
                  href="https://www.instagram.com/jarthaztoursandtravel/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="hover:text-jarthaz-gold transition-colors"
                >
                  <Instagram size={20} />
                  <span className="sr-only">Instagram</span>
                </Link>
              </div>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4 text-jarthaz-gold">Quick Links</h3>
              <ul className="space-y-2 text-sm">
                <li>
                  <Link href="/" className="hover:text-jarthaz-gold transition-colors inline-flex items-center">
                    <span className="w-1.5 h-1.5 bg-jarthaz-gold rounded-full mr-2"></span>
                    Home
                  </Link>
                </li>
                <li>
                  <Link href="/about" className="hover:text-jarthaz-gold transition-colors inline-flex items-center">
                    <span className="w-1.5 h-1.5 bg-jarthaz-gold rounded-full mr-2"></span>
                    About Us
                  </Link>
                </li>
                <li>
                  <Link
                    href="/destinations"
                    className="hover:text-jarthaz-gold transition-colors inline-flex items-center"
                  >
                    <span className="w-1.5 h-1.5 bg-jarthaz-gold rounded-full mr-2"></span>
                    Destinations
                  </Link>
                </li>
                <li>
                  <Link href="/contact" className="hover:text-jarthaz-gold transition-colors inline-flex items-center">
                    <span className="w-1.5 h-1.5 bg-jarthaz-gold rounded-full mr-2"></span>
                    Contact
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4 text-jarthaz-gold">Popular Packages</h3>
              <ul className="space-y-2 text-sm">
                <li>
                  <Link
                    href="/packages/gorilla-trekking"
                    className="hover:text-jarthaz-gold transition-colors inline-flex items-center"
                  >
                    <span className="w-1.5 h-1.5 bg-jarthaz-gold rounded-full mr-2"></span>
                    Gorilla Trekking
                  </Link>
                </li>
                <li>
                  <Link
                    href="/packages/wildlife-safaris"
                    className="hover:text-jarthaz-gold transition-colors inline-flex items-center"
                  >
                    <span className="w-1.5 h-1.5 bg-jarthaz-gold rounded-full mr-2"></span>
                    Wildlife Safaris
                  </Link>
                </li>
                <li>
                  <Link
                    href="/packages/adventure-escapades"
                    className="hover:text-jarthaz-gold transition-colors inline-flex items-center"
                  >
                    <span className="w-1.5 h-1.5 bg-jarthaz-gold rounded-full mr-2"></span>
                    Adventure Escapades
                  </Link>
                </li>
                <li>
                  <Link
                    href="/packages/cultural-tours"
                    className="hover:text-jarthaz-gold transition-colors inline-flex items-center"
                  >
                    <span className="w-1.5 h-1.5 bg-jarthaz-gold rounded-full mr-2"></span>
                    Cultural Tours
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4 text-jarthaz-gold">Contact Us</h3>
              <address className="not-italic text-sm space-y-3 text-gray-300">
                <p className="flex items-start">
                  <MapPin size={18} className="text-jarthaz-gold mr-2 mt-0.5 flex-shrink-0" />
                  <span>Kataza, Kampala, Uganda</span>
                </p>
                <p className="flex items-start">
                  <Mail size={18} className="text-jarthaz-gold mr-2 mt-0.5 flex-shrink-0" />
                  <span><EMAIL></span>
                </p>
                <p className="flex items-start">
                  <Phone size={18} className="text-jarthaz-gold mr-2 mt-0.5 flex-shrink-0" />
                  <span>+256-788-359-512</span>
                </p>
                <p className="flex items-start">
                  <Phone size={18} className="text-jarthaz-gold mr-2 mt-0.5 flex-shrink-0" />
                  <span>+256-759-820-874 (WhatsApp)</span>
                </p>
              </address>
            </div>
          </div>
          <div className="mt-8 pt-8 border-t border-gray-800 text-sm text-gray-400 flex flex-col md:flex-row justify-between items-center">
            <div className="flex flex-col md:flex-row items-center gap-2 md:gap-4">
              <p>&copy; {currentYear} Jarthaz Tours. All rights reserved.</p>
              <div className="flex items-center gap-1 text-xs">
                <span>Developed by</span>
                <Link
                  href="https://cavemotions.com/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-jarthaz-gold hover:text-jarthaz-gold/80 transition-colors font-medium inline-flex items-center gap-1"
                >
                  <span className="relative">
                    Cave Motions
                    <span className="absolute -bottom-0.5 left-0 w-full h-0.5 bg-gradient-to-r from-jarthaz-gold to-transparent opacity-0 hover:opacity-100 transition-opacity"></span>
                  </span>
                  <span className="text-jarthaz-gold/70 text-xs">↗</span>
                </Link>
              </div>
            </div>
            <div className="mt-4 md:mt-0">
              <Link href="/privacy-policy" className="hover:text-jarthaz-gold transition-colors">
                Privacy Policy
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
