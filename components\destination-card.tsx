"use client"

import Image from "next/image"
import Link from "next/link"
import { MapPin } from "lucide-react"
import { motion } from "framer-motion"

interface DestinationCardProps {
  name: string
  type: string
  image: string
  alt: string
  delay?: number
}

export default function DestinationCard({ name, type, image, alt, delay = 0 }: DestinationCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay }}
      viewport={{ once: true, margin: "-100px" }}
    >
      <Link href="/destinations" className="group block">
        <div className="destination-card group relative h-64 overflow-hidden rounded-lg">
          <Image src={image || "/placeholder.svg"} alt={alt} fill className="object-cover" />
          <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>

          {/* Decorative corner */}
          <div className="absolute top-4 right-4 w-12 h-12 border-t-2 border-r-2 border-jarthaz-gold/50 rounded-tr-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <div className="absolute bottom-4 left-4 w-12 h-12 border-b-2 border-l-2 border-jarthaz-gold/50 rounded-bl-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

          <div className="absolute bottom-0 left-0 p-4 text-white z-10 transform translate-y-2 group-hover:translate-y-0 transition-transform duration-300">
            <div className="flex items-center mb-2">
              <MapPin size={16} className="mr-1 text-jarthaz-gold" />
              <span className="text-sm">{type}</span>
            </div>
            <h3 className="font-bold text-lg text-shadow">{name}</h3>
            <div className="h-0.5 w-0 bg-jarthaz-gold group-hover:w-full transition-all duration-500 mt-2"></div>
          </div>
        </div>
      </Link>
    </motion.div>
  )
}
