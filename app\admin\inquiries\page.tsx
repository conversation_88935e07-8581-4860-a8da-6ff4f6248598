import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Search, Filter, Mail, CheckCircle, Clock, AlertCircle, Eye, Archive, Trash2 } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

// Mock data for inquiries
const inquiries = [
  {
    id: "inq-001",
    name: "<PERSON>",
    email: "<EMAIL>",
    subject: "Gorilla Trekking Inquiry",
    date: "2023-05-12",
    status: "new",
  },
  {
    id: "inq-002",
    name: "<PERSON>",
    email: "<EMAIL>",
    subject: "Wildlife Safari Package",
    date: "2023-05-11",
    status: "replied",
  },
  {
    id: "inq-003",
    name: "<PERSON>",
    email: "<EMAIL>",
    subject: "Honeymoon Package Availability",
    date: "2023-05-10",
    status: "pending",
  },
  {
    id: "inq-004",
    name: "<PERSON>",
    email: "<EMAIL>",
    subject: "Custom Tour Request",
    date: "2023-05-09",
    status: "new",
  },
  {
    id: "inq-005",
    name: "David Wilson",
    email: "<EMAIL>",
    subject: "Booking Confirmation",
    date: "2023-05-08",
    status: "replied",
  },
  {
    id: "inq-006",
    name: "Jennifer Lee",
    email: "<EMAIL>",
    subject: "Adventure Tour Question",
    date: "2023-05-07",
    status: "pending",
  },
  {
    id: "inq-007",
    name: "Robert Taylor",
    email: "<EMAIL>",
    subject: "Group Booking Inquiry",
    date: "2023-05-06",
    status: "archived",
  },
]

const getStatusIcon = (status: string) => {
  switch (status) {
    case "new":
      return <Mail className="h-4 w-4 text-blue-500" />
    case "replied":
      return <CheckCircle className="h-4 w-4 text-green-500" />
    case "pending":
      return <Clock className="h-4 w-4 text-amber-500" />
    case "archived":
      return <Archive className="h-4 w-4 text-gray-500" />
    default:
      return <AlertCircle className="h-4 w-4 text-red-500" />
  }
}

const getStatusBadge = (status: string) => {
  switch (status) {
    case "new":
      return (
        <Badge variant="outline" className="bg-blue-50 text-blue-700 hover:bg-blue-50 border-blue-200">
          New
        </Badge>
      )
    case "replied":
      return (
        <Badge variant="outline" className="bg-green-50 text-green-700 hover:bg-green-50 border-green-200">
          Replied
        </Badge>
      )
    case "pending":
      return (
        <Badge variant="outline" className="bg-amber-50 text-amber-700 hover:bg-amber-50 border-amber-200">
          Pending
        </Badge>
      )
    case "archived":
      return (
        <Badge variant="outline" className="bg-gray-50 text-gray-700 hover:bg-gray-50 border-gray-200">
          Archived
        </Badge>
      )
    default:
      return <Badge variant="outline">Unknown</Badge>
  }
}

export default function InquiriesPage() {
  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Inquiries</h2>
      </div>

      <div className="flex items-center gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input type="search" placeholder="Search inquiries..." className="pl-8" />
        </div>
        <Button variant="outline">
          <Filter className="mr-2 h-4 w-4" /> Filter
        </Button>
      </div>

      <Tabs defaultValue="all" className="space-y-4">
        <TabsList>
          <TabsTrigger value="all">All</TabsTrigger>
          <TabsTrigger value="new">New</TabsTrigger>
          <TabsTrigger value="replied">Replied</TabsTrigger>
          <TabsTrigger value="pending">Pending</TabsTrigger>
          <TabsTrigger value="archived">Archived</TabsTrigger>
        </TabsList>
        <TabsContent value="all" className="space-y-4">
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[100px]">Status</TableHead>
                    <TableHead>Name</TableHead>
                    <TableHead>Subject</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {inquiries.map((inquiry) => (
                    <TableRow key={inquiry.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getStatusIcon(inquiry.status)}
                          {getStatusBadge(inquiry.status)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">{inquiry.name}</div>
                        <div className="text-sm text-muted-foreground">{inquiry.email}</div>
                      </TableCell>
                      <TableCell>{inquiry.subject}</TableCell>
                      <TableCell>{inquiry.date}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button variant="ghost" size="icon">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon">
                            <Archive className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="new" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>New Inquiries</CardTitle>
              <CardDescription>View and respond to new inquiries from potential customers.</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Subject</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {inquiries
                    .filter((i) => i.status === "new")
                    .map((inquiry) => (
                      <TableRow key={inquiry.id}>
                        <TableCell>
                          <div className="font-medium">{inquiry.name}</div>
                          <div className="text-sm text-muted-foreground">{inquiry.email}</div>
                        </TableCell>
                        <TableCell>{inquiry.subject}</TableCell>
                        <TableCell>{inquiry.date}</TableCell>
                        <TableCell className="text-right">
                          <Button size="sm">Respond</Button>
                        </TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="replied" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Replied Inquiries</CardTitle>
              <CardDescription>Inquiries that have been responded to.</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Subject</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {inquiries
                    .filter((i) => i.status === "replied")
                    .map((inquiry) => (
                      <TableRow key={inquiry.id}>
                        <TableCell>
                          <div className="font-medium">{inquiry.name}</div>
                          <div className="text-sm text-muted-foreground">{inquiry.email}</div>
                        </TableCell>
                        <TableCell>{inquiry.subject}</TableCell>
                        <TableCell>{inquiry.date}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button variant="outline" size="sm">
                              View
                            </Button>
                            <Button variant="ghost" size="sm">
                              Archive
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="pending" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Pending Inquiries</CardTitle>
              <CardDescription>Inquiries awaiting further action.</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Subject</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {inquiries
                    .filter((i) => i.status === "pending")
                    .map((inquiry) => (
                      <TableRow key={inquiry.id}>
                        <TableCell>
                          <div className="font-medium">{inquiry.name}</div>
                          <div className="text-sm text-muted-foreground">{inquiry.email}</div>
                        </TableCell>
                        <TableCell>{inquiry.subject}</TableCell>
                        <TableCell>{inquiry.date}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button size="sm">Respond</Button>
                            <Button variant="outline" size="sm">
                              Mark as Resolved
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="archived" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Archived Inquiries</CardTitle>
              <CardDescription>Previously archived inquiries.</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Subject</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {inquiries
                    .filter((i) => i.status === "archived")
                    .map((inquiry) => (
                      <TableRow key={inquiry.id}>
                        <TableCell>
                          <div className="font-medium">{inquiry.name}</div>
                          <div className="text-sm text-muted-foreground">{inquiry.email}</div>
                        </TableCell>
                        <TableCell>{inquiry.subject}</TableCell>
                        <TableCell>{inquiry.date}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button variant="outline" size="sm">
                              Restore
                            </Button>
                            <Button variant="destructive" size="sm">
                              Delete
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
