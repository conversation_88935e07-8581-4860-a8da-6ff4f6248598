"use client"

import Image from "next/image"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Check, ArrowLeft, Users, Clock, MapPin, Star, ChevronRight } from "lucide-react"
import AnimatedSection from "@/components/animated-section"
import { motion } from "framer-motion"

export default function GorillaTrekkingPage() {
  // Testimonials data
  const testimonials = [
    {
      name: "<PERSON>",
      location: "United States",
      text: "The gorilla trekking experience was absolutely life-changing. Our guide was knowledgeable and made sure we had the best possible encounter with these magnificent creatures.",
      rating: 5,
      image: "/placeholder.svg?height=100&width=100",
    },
    {
      name: "<PERSON>",
      location: "Canada",
      text: "Worth every penny! The moment when you first see the gorillas in their natural habitat is indescribable. Jarthaz Tours handled everything perfectly.",
      rating: 5,
      image: "/placeholder.svg?height=100&width=100",
    },
    {
      name: "<PERSON>",
      location: "Australia",
      text: "A bucket list experience that exceeded all expectations. The accommodations were comfortable and the staff was incredibly friendly and helpful.",
      rating: 4,
      image: "/placeholder.svg?height=100&width=100",
    },
  ]

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative h-[80vh] flex items-center overflow-hidden">
        <div className="absolute inset-0 z-0">
          <Image
            src="/images/gorillas/gorilla-family.jpeg"
            alt="Mountain gorilla family in Bwindi Impenetrable Forest"
            fill
            className="object-cover"
            priority
          />
          <div className="hero-overlay"></div>
        </div>

        {/* Decorative elements */}
        <div className="absolute top-20 right-20 w-40 h-40 border-r-2 border-t-2 border-jarthaz-gold/30 z-10"></div>
        <div className="absolute bottom-20 left-20 w-40 h-40 border-l-2 border-b-2 border-jarthaz-gold/30 z-10"></div>

        <div className="container relative z-10 text-white">
          <motion.div
            className="max-w-3xl space-y-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <Link href="/destinations" className="inline-flex items-center text-white/90 hover:text-white mb-4 group">
              <ArrowLeft size={16} className="mr-2 group-hover:-translate-x-1 transition-transform" />
              Back to Destinations
            </Link>

            <div className="inline-block bg-jarthaz-gold/20 backdrop-blur-sm px-4 py-1 rounded-full text-white text-sm font-medium">
              Premium Experience
            </div>

            <h1 className="text-4xl font-bold tracking-tighter sm:text-5xl md:text-6xl text-shadow">
              Gorilla Trekking <span className="text-jarthaz-gold">Adventures</span>
            </h1>

            <p className="text-xl text-white/90 max-w-2xl">
              Embark on a once-in-a-lifetime journey to meet Uganda's gentle giants in their natural habitat.
            </p>

            {/* Package highlights */}
            <div className="flex flex-wrap gap-4 mt-4">
              <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm px-3 py-1.5 rounded-full">
                <Clock size={16} className="text-jarthaz-gold" />
                <span className="text-sm">3-5 days</span>
              </div>
              <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm px-3 py-1.5 rounded-full">
                <Users size={16} className="text-jarthaz-gold" />
                <span className="text-sm">Small groups (2-8)</span>
              </div>
              <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm px-3 py-1.5 rounded-full">
                <MapPin size={16} className="text-jarthaz-gold" />
                <span className="text-sm">Bwindi Impenetrable Forest</span>
              </div>
              <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm px-3 py-1.5 rounded-full">
                <Star size={16} className="text-jarthaz-gold" />
                <span className="text-sm">5.0 (124 reviews)</span>
              </div>
            </div>

            <div className="pt-6">
              <Button size="lg" variant="safari" className="btn-shimmer" asChild>
                <Link href="#booking">Book This Experience</Link>
              </Button>
            </div>
          </motion.div>
        </div>

        <div className="absolute bottom-0 left-0 right-0">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320" className="text-background fill-current">
            <path d="M0,224L48,213.3C96,203,192,181,288,181.3C384,181,480,203,576,202.7C672,203,768,181,864,181.3C960,181,1056,203,1152,208C1248,213,1344,203,1392,197.3L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path>
          </svg>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-20">
        <div className="container">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {/* Left Content */}
            <div className="lg:col-span-2 space-y-12">
              <AnimatedSection>
                <div className="bg-white dark:bg-jarthaz-black/20 rounded-lg p-8 shadow-md">
                  <div className="inline-block bg-jarthaz-gold/10 px-4 py-1 rounded-full text-jarthaz-gold text-sm font-medium mb-4">
                    About The Experience
                  </div>
                  <h2 className="section-heading text-3xl">Gorilla Trekking</h2>
                  <div className="safari-divider w-24 my-6"></div>
                  <p className="text-muted-foreground mb-4">
                    Gorilla trekking is a profound wildlife experience that brings you face-to-face with one of our
                    closest relatives in the animal kingdom. Uganda is home to over half of the world's remaining
                    mountain gorilla population, making it one of the best places on earth for this extraordinary
                    encounter.
                  </p>
                  <p className="text-muted-foreground mb-4">
                    The trek takes you through the misty forests of Bwindi Impenetrable National Park or Mgahinga
                    Gorilla National Park, where experienced guides lead you to habituated gorilla families. Once you
                    find them, you'll spend a magical hour observing these gentle giants as they feed, play, and
                    interact with each other.
                  </p>
                  <p className="text-muted-foreground">
                    This experience not only offers incredible wildlife viewing but also contributes directly to
                    conservation efforts and supports local communities. It's a journey that will leave you with
                    memories to last a lifetime.
                  </p>
                </div>
              </AnimatedSection>

              <AnimatedSection delay={0.2}>
                <div className="relative h-[500px] rounded-lg overflow-hidden shadow-xl">
                  <Image
                    src="/images/gorillas/silverback-portrait.jpeg"
                    alt="Silverback gorilla in Bwindi Forest"
                    fill
                    className="object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-jarthaz-black/50 to-transparent"></div>

                  {/* Image caption */}
                  <div className="absolute bottom-0 left-0 right-0 p-6 bg-jarthaz-black/60 backdrop-blur-sm">
                    <h3 className="text-white font-medium text-lg">Mountain Gorilla in Bwindi Impenetrable Forest</h3>
                    <p className="text-white/80 text-sm">
                      Uganda is home to over half of the world's remaining mountain gorilla population
                    </p>
                  </div>
                </div>
              </AnimatedSection>

              <AnimatedSection delay={0.3}>
                <div className="bg-white dark:bg-jarthaz-black/20 rounded-lg p-8 shadow-md">
                  <div className="inline-block bg-jarthaz-gold/10 px-4 py-1 rounded-full text-jarthaz-gold text-sm font-medium mb-4">
                    What's Included
                  </div>
                  <h2 className="section-heading text-3xl">Package Includes</h2>
                  <div className="safari-divider w-24 my-6"></div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <ul className="space-y-4">
                      <li className="flex items-start gap-3">
                        <div className="w-6 h-6 rounded-full bg-jarthaz-gold/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                          <Check size={14} className="text-jarthaz-gold" />
                        </div>
                        <span>Guided treks in Bwindi Impenetrable National Park or Mgahinga Gorilla National Park</span>
                      </li>
                      <li className="flex items-start gap-3">
                        <div className="w-6 h-6 rounded-full bg-jarthaz-gold/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                          <Check size={14} className="text-jarthaz-gold" />
                        </div>
                        <span>Comfortable accommodation and meals throughout your stay</span>
                      </li>
                      <li className="flex items-start gap-3">
                        <div className="w-6 h-6 rounded-full bg-jarthaz-gold/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                          <Check size={14} className="text-jarthaz-gold" />
                        </div>
                        <span>Park entry and gorilla trekking permits</span>
                      </li>
                      <li className="flex items-start gap-3">
                        <div className="w-6 h-6 rounded-full bg-jarthaz-gold/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                          <Check size={14} className="text-jarthaz-gold" />
                        </div>
                        <span>Cultural interactions with local communities</span>
                      </li>
                    </ul>

                    <ul className="space-y-4">
                      <li className="flex items-start gap-3">
                        <div className="w-6 h-6 rounded-full bg-jarthaz-gold/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                          <Check size={14} className="text-jarthaz-gold" />
                        </div>
                        <span>Transportation in a comfortable 4x4 safari vehicle</span>
                      </li>
                      <li className="flex items-start gap-3">
                        <div className="w-6 h-6 rounded-full bg-jarthaz-gold/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                          <Check size={14} className="text-jarthaz-gold" />
                        </div>
                        <span>Professional English-speaking guide/driver</span>
                      </li>
                      <li className="flex items-start gap-3">
                        <div className="w-6 h-6 rounded-full bg-jarthaz-gold/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                          <Check size={14} className="text-jarthaz-gold" />
                        </div>
                        <span>Bottled water throughout the journey</span>
                      </li>
                      <li className="flex items-start gap-3">
                        <div className="w-6 h-6 rounded-full bg-jarthaz-gold/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                          <Check size={14} className="text-jarthaz-gold" />
                        </div>
                        <span>All applicable taxes and fees</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </AnimatedSection>

              <AnimatedSection delay={0.4}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="relative h-[300px] rounded-lg overflow-hidden shadow-md">
                    <Image
                      src="/images/gorillas/gorilla-parent-child.jpeg"
                      alt="Adult gorilla with baby in Bwindi Forest"
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-jarthaz-black/70 to-transparent"></div>
                    <div className="absolute bottom-0 left-0 right-0 p-4">
                      <p className="text-white text-sm">Adult gorilla with a young gorilla in Bwindi Forest</p>
                    </div>
                  </div>
                  <div className="relative h-[300px] rounded-lg overflow-hidden shadow-md">
                    <Image
                      src="https://images.unsplash.com/photo-1509897739002-791fa79aac9b?fm=jpg&q=80&w=2000&ixlib=rb-4.1.0"
                      alt="Mountain gorilla sitting in lush green forest in Bwindi Impenetrable Forest, Uganda"
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-jarthaz-black/70 to-transparent"></div>
                    <div className="absolute bottom-0 left-0 right-0 p-4">
                      <p className="text-white text-sm">Spotting gorillas through the dense forest vegetation</p>
                    </div>
                  </div>
                </div>
              </AnimatedSection>

              <AnimatedSection delay={0.5}>
                <div className="bg-white dark:bg-jarthaz-black/20 rounded-lg p-8 shadow-md">
                  <div className="inline-block bg-jarthaz-gold/10 px-4 py-1 rounded-full text-jarthaz-gold text-sm font-medium mb-4">
                    Sample Itinerary
                  </div>
                  <h2 className="section-heading text-3xl">3 Days Gorilla Trekking</h2>
                  <div className="safari-divider w-24 my-6"></div>

                  <div className="space-y-8 mt-6">
                    <div className="relative pl-8 pb-8 border-l-2 border-jarthaz-gold/30">
                      <div className="absolute top-0 left-0 w-8 h-8 -translate-x-1/2 rounded-full bg-jarthaz-gold flex items-center justify-center">
                        <span className="text-jarthaz-black font-bold">1</span>
                      </div>
                      <h3 className="font-bold text-xl mb-3">Day 1: Journey to Bwindi</h3>
                      <div className="bg-muted/30 p-6 rounded-lg">
                        <p className="text-muted-foreground">
                          Your adventure begins with an early morning pickup from your hotel in Kampala or Entebbe.
                          Travel southwest through Uganda's beautiful countryside, with a stop at the equator for photos
                          and souvenir shopping. Enjoy lunch en route and continue to Bwindi Impenetrable National Park.
                          Arrive in the evening at your accommodation near the park, where you'll have dinner and a
                          briefing about the next day's gorilla trekking experience.
                        </p>
                        <div className="flex flex-wrap gap-4 mt-4 text-sm">
                          <div className="flex items-center gap-1">
                            <Clock size={14} className="text-jarthaz-gold" />
                            <span className="font-medium">Duration:</span> 8-10 hours drive
                          </div>
                          <div className="flex items-center gap-1">
                            <MapPin size={14} className="text-jarthaz-gold" />
                            <span className="font-medium">Accommodation:</span> Lodge near Bwindi
                          </div>
                          <div className="flex items-center gap-1">
                            <Users size={14} className="text-jarthaz-gold" />
                            <span className="font-medium">Meals:</span> Lunch, Dinner
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="relative pl-8 pb-8 border-l-2 border-jarthaz-gold/30">
                      <div className="absolute top-0 left-0 w-8 h-8 -translate-x-1/2 rounded-full bg-jarthaz-gold flex items-center justify-center">
                        <span className="text-jarthaz-black font-bold">2</span>
                      </div>
                      <h3 className="font-bold text-xl mb-3">Day 2: Gorilla Trekking</h3>
                      <div className="bg-muted/30 p-6 rounded-lg">
                        <p className="text-muted-foreground">
                          After an early breakfast, head to the park headquarters for a briefing from the rangers about
                          gorilla trekking protocols. Then, embark on your trek through the forest in search of a
                          gorilla family. The trek can take anywhere from 30 minutes to several hours, depending on the
                          gorillas' location. Once you find them, you'll spend a magical hour observing these
                          magnificent creatures in their natural habitat. Return to your lodge for lunch and relaxation.
                          In the afternoon, you can opt for a community visit to learn about local culture and
                          traditions.
                        </p>
                        <div className="flex flex-wrap gap-4 mt-4 text-sm">
                          <div className="flex items-center gap-1">
                            <Clock size={14} className="text-jarthaz-gold" />
                            <span className="font-medium">Duration:</span> 2-6 hours trekking
                          </div>
                          <div className="flex items-center gap-1">
                            <MapPin size={14} className="text-jarthaz-gold" />
                            <span className="font-medium">Accommodation:</span> Same lodge near Bwindi
                          </div>
                          <div className="flex items-center gap-1">
                            <Users size={14} className="text-jarthaz-gold" />
                            <span className="font-medium">Meals:</span> Breakfast, Lunch, Dinner
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="relative pl-8">
                      <div className="absolute top-0 left-0 w-8 h-8 -translate-x-1/2 rounded-full bg-jarthaz-gold flex items-center justify-center">
                        <span className="text-jarthaz-black font-bold">3</span>
                      </div>
                      <h3 className="font-bold text-xl mb-3">Day 3: Return to Kampala/Entebbe</h3>
                      <div className="bg-muted/30 p-6 rounded-lg">
                        <p className="text-muted-foreground">
                          After breakfast, begin your journey back to Kampala or Entebbe. Enjoy the scenic drive with
                          stops for photo opportunities and lunch. You'll also have the chance for cultural encounters
                          along the way. Arrive in Kampala or Entebbe in the evening, where you'll be dropped off at
                          your hotel or the airport for your onward journey.
                        </p>
                        <div className="flex flex-wrap gap-4 mt-4 text-sm">
                          <div className="flex items-center gap-1">
                            <Clock size={14} className="text-jarthaz-gold" />
                            <span className="font-medium">Duration:</span> 8-10 hours drive
                          </div>
                          <div className="flex items-center gap-1">
                            <Users size={14} className="text-jarthaz-gold" />
                            <span className="font-medium">Meals:</span> Breakfast, Lunch
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </AnimatedSection>

              <AnimatedSection delay={0.6}>
                <div className="relative h-[500px] rounded-lg overflow-hidden shadow-xl">
                  <Image
                    src="/images/gorillas/gorilla-family-baby.jpeg"
                    alt="Gorilla family with baby"
                    fill
                    className="object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-jarthaz-black/50 to-transparent"></div>

                  {/* Image caption */}
                  <div className="absolute bottom-0 left-0 right-0 p-6 bg-jarthaz-black/60 backdrop-blur-sm">
                    <h3 className="text-white font-medium text-lg">Gorilla Family with Baby</h3>
                    <p className="text-white/80 text-sm">
                      Witnessing the family dynamics of gorillas is one of the most rewarding aspects of gorilla
                      trekking
                    </p>
                  </div>
                </div>
              </AnimatedSection>

              <AnimatedSection delay={0.7}>
                <div className="bg-white dark:bg-jarthaz-black/20 rounded-lg p-8 shadow-md">
                  <div className="inline-block bg-jarthaz-gold/10 px-4 py-1 rounded-full text-jarthaz-gold text-sm font-medium mb-4">
                    Essential Information
                  </div>
                  <h2 className="section-heading text-3xl">Important Information</h2>
                  <div className="safari-divider w-24 my-6"></div>

                  <div className="space-y-6">
                    <div>
                      <h3 className="font-semibold text-lg flex items-center gap-2">
                        <div className="w-6 h-6 rounded-full bg-jarthaz-gold/20 flex items-center justify-center flex-shrink-0">
                          <Users size={14} className="text-jarthaz-gold" />
                        </div>
                        Physical Requirements
                      </h3>
                      <p className="text-muted-foreground mt-2">
                        Gorilla trekking requires a reasonable level of fitness as the terrain can be steep, muddy, and
                        challenging. The trek can last from 30 minutes to several hours depending on the location of the
                        gorillas.
                      </p>
                    </div>

                    <div>
                      <h3 className="font-semibold text-lg flex items-center gap-2">
                        <div className="w-6 h-6 rounded-full bg-jarthaz-gold/20 flex items-center justify-center flex-shrink-0">
                          <Check size={14} className="text-jarthaz-gold" />
                        </div>
                        What to Bring
                      </h3>
                      <ul className="text-muted-foreground mt-2 grid grid-cols-1 md:grid-cols-2 gap-2">
                        <li className="flex items-center gap-2">
                          <ChevronRight size={14} className="text-jarthaz-gold" />
                          Hiking boots or sturdy walking shoes
                        </li>
                        <li className="flex items-center gap-2">
                          <ChevronRight size={14} className="text-jarthaz-gold" />
                          Long-sleeved shirts and long pants
                        </li>
                        <li className="flex items-center gap-2">
                          <ChevronRight size={14} className="text-jarthaz-gold" />
                          Rain jacket or poncho
                        </li>
                        <li className="flex items-center gap-2">
                          <ChevronRight size={14} className="text-jarthaz-gold" />
                          Garden gloves for gripping vegetation
                        </li>
                        <li className="flex items-center gap-2">
                          <ChevronRight size={14} className="text-jarthaz-gold" />
                          Hat and sunscreen
                        </li>
                        <li className="flex items-center gap-2">
                          <ChevronRight size={14} className="text-jarthaz-gold" />
                          Insect repellent
                        </li>
                        <li className="flex items-center gap-2">
                          <ChevronRight size={14} className="text-jarthaz-gold" />
                          Camera (no flash photography)
                        </li>
                        <li className="flex items-center gap-2">
                          <ChevronRight size={14} className="text-jarthaz-gold" />
                          Water bottle
                        </li>
                      </ul>
                    </div>

                    <div>
                      <h3 className="font-semibold text-lg flex items-center gap-2">
                        <div className="w-6 h-6 rounded-full bg-jarthaz-gold/20 flex items-center justify-center flex-shrink-0">
                          <Check size={14} className="text-jarthaz-gold" />
                        </div>
                        Gorilla Trekking Rules
                      </h3>
                      <ul className="text-muted-foreground mt-2 space-y-2">
                        <li className="flex items-start gap-2">
                          <ChevronRight size={14} className="text-jarthaz-gold mt-1" />
                          <span>Keep a minimum distance of 7 meters (21 feet) from the gorillas</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <ChevronRight size={14} className="text-jarthaz-gold mt-1" />
                          <span>No eating, drinking, or smoking near the gorillas</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <ChevronRight size={14} className="text-jarthaz-gold mt-1" />
                          <span>Speak in low voices or whisper when near the gorillas</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <ChevronRight size={14} className="text-jarthaz-gold mt-1" />
                          <span>No flash photography</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <ChevronRight size={14} className="text-jarthaz-gold mt-1" />
                          <span>Follow ranger instructions at all times</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <ChevronRight size={14} className="text-jarthaz-gold mt-1" />
                          <span>Do not trek if you have a contagious illness</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </AnimatedSection>

              {/* Testimonials */}
              <AnimatedSection delay={0.8}>
                <div className="bg-white dark:bg-jarthaz-black/20 rounded-lg p-8 shadow-md">
                  <div className="inline-block bg-jarthaz-gold/10 px-4 py-1 rounded-full text-jarthaz-gold text-sm font-medium mb-4">
                    Guest Experiences
                  </div>
                  <h2 className="section-heading text-3xl">Testimonials</h2>
                  <div className="safari-divider w-24 my-6"></div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {testimonials.map((testimonial, index) => (
                      <div key={index} className="bg-muted/30 p-6 rounded-lg">
                        <div className="flex items-start gap-4">
                          <div className="relative w-12 h-12 rounded-full overflow-hidden">
                            <Image
                              src={testimonial.image || "/placeholder.svg"}
                              alt={testimonial.name}
                              fill
                              className="object-cover"
                            />
                          </div>
                          <div>
                            <h4 className="font-bold">{testimonial.name}</h4>
                            <p className="text-sm text-muted-foreground">{testimonial.location}</p>
                            <div className="flex items-center gap-1 mt-1">
                              {[...Array(5)].map((_, i) => (
                                <Star
                                  key={i}
                                  size={14}
                                  className={
                                    i < testimonial.rating ? "text-jarthaz-gold fill-jarthaz-gold" : "text-muted"
                                  }
                                />
                              ))}
                            </div>
                          </div>
                        </div>
                        <p className="mt-4 text-muted-foreground italic">"{testimonial.text}"</p>
                      </div>
                    ))}
                  </div>
                </div>
              </AnimatedSection>
            </div>

            {/* Right Sidebar */}
            <div className="space-y-8">
              <AnimatedSection direction="left">
                <div id="booking" className="bg-white dark:bg-jarthaz-black/20 rounded-lg p-6 shadow-md sticky top-24">
                  <h3 className="text-xl font-bold mb-4 text-jarthaz-gold">Book This Experience</h3>
                  <div className="safari-divider w-16 my-4"></div>

                  <div className="space-y-4 mb-6">
                    <div className="flex justify-between items-center pb-2 border-b border-border">
                      <span className="text-sm">Starting Price</span>

                    </div>
                    <div className="flex justify-between items-center pb-2 border-b border-border">
                      <span className="text-sm">Duration</span>
                      <span>3-5 days</span>
                    </div>
                    <div className="flex justify-between items-center pb-2 border-b border-border">
                      <span className="text-sm">Group Size</span>
                      <span>2-8 people</span>
                    </div>
                    <div className="flex justify-between items-center pb-2 border-b border-border">
                      <span className="text-sm">Difficulty</span>
                      <span>Moderate</span>
                    </div>
                    <div className="flex justify-between items-center pb-2 border-b border-border">
                      <span className="text-sm">Permit Required</span>
                      <span>Yes ($700 included)</span>
                    </div>
                  </div>

                  <p className="mb-6 text-sm text-muted-foreground">
                    Secure your gorilla trekking permit early as they are limited and in high demand.
                  </p>

                  <Button variant="safari" size="lg" className="w-full btn-shimmer" asChild>
                    <Link href="/contact">Contact Us to Book</Link>
                  </Button>
                </div>
              </AnimatedSection>

              <AnimatedSection direction="left" delay={0.2}>
                <div className="bg-white dark:bg-jarthaz-black/20 rounded-lg p-6 shadow-md">
                  <h3 className="text-xl font-bold mb-4 text-jarthaz-gold">Package Highlights</h3>
                  <div className="safari-divider w-16 my-4"></div>

                  <ul className="space-y-3">
                    <li className="flex items-start gap-3">
                      <div className="w-6 h-6 rounded-full bg-jarthaz-gold/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                        <Check size={14} className="text-jarthaz-gold" />
                      </div>
                      <span className="text-sm">Face-to-face encounter with mountain gorillas</span>
                    </li>
                    <li className="flex items-start gap-3">
                      <div className="w-6 h-6 rounded-full bg-jarthaz-gold/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                        <Check size={14} className="text-jarthaz-gold" />
                      </div>
                      <span className="text-sm">Expert guides and trackers</span>
                    </li>
                    <li className="flex items-start gap-3">
                      <div className="w-6 h-6 rounded-full bg-jarthaz-gold/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                        <Check size={14} className="text-jarthaz-gold" />
                      </div>
                      <span className="text-sm">Stunning forest landscapes</span>
                    </li>
                    <li className="flex items-start gap-3">
                      <div className="w-6 h-6 rounded-full bg-jarthaz-gold/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                        <Check size={14} className="text-jarthaz-gold" />
                      </div>
                      <span className="text-sm">Cultural experiences with local communities</span>
                    </li>
                    <li className="flex items-start gap-3">
                      <div className="w-6 h-6 rounded-full bg-jarthaz-gold/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                        <Check size={14} className="text-jarthaz-gold" />
                      </div>
                      <span className="text-sm">Comfortable accommodation in scenic locations</span>
                    </li>
                  </ul>
                </div>
              </AnimatedSection>

              <AnimatedSection direction="left" delay={0.3}>
                <div className="bg-white dark:bg-jarthaz-black/20 rounded-lg p-6 shadow-md">
                  <h3 className="text-xl font-bold mb-4 text-jarthaz-gold">Best Time to Visit</h3>
                  <div className="safari-divider w-16 my-4"></div>

                  <p className="text-sm text-muted-foreground mb-4">
                    Gorilla trekking is available year-round, but the best times are during the dry seasons:
                  </p>

                  <div className="space-y-3 mb-4">
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 rounded-full bg-jarthaz-gold/20 flex items-center justify-center">
                        <div className="w-2 h-2 rounded-full bg-jarthaz-gold"></div>
                      </div>
                      <span>December to February</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 rounded-full bg-jarthaz-gold/20 flex items-center justify-center">
                        <div className="w-2 h-2 rounded-full bg-jarthaz-gold"></div>
                      </div>
                      <span>June to August</span>
                    </div>
                  </div>

                  <p className="text-sm text-muted-foreground">
                    These periods offer easier hiking conditions with drier trails. However, permits are in high demand
                    during these times, so early booking is essential.
                  </p>
                </div>
              </AnimatedSection>

              <AnimatedSection direction="left" delay={0.4}>
                <div className="bg-white dark:bg-jarthaz-black/20 rounded-lg overflow-hidden shadow-md">
                  <div className="relative h-48">
                    <Image
                      src="/images/gorillas/gorilla-closeup.jpeg"
                      alt="Close-up of mountain gorilla"
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-jarthaz-black/70 to-transparent"></div>
                    <div className="absolute bottom-0 left-0 w-full p-4 text-white">
                      <h3 className="font-semibold text-lg">Conservation Note</h3>
                    </div>
                  </div>
                  <div className="p-4">
                    <p className="text-sm text-muted-foreground">
                      A portion of your gorilla permit fee goes directly to conservation efforts and community
                      development projects around the national parks, helping to ensure the survival of these
                      magnificent creatures for generations to come.
                    </p>
                    <div className="mt-4 pt-4 border-t border-border">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Conservation Contribution</span>
                        <span className="text-sm">$200 per permit</span>
                      </div>
                    </div>
                  </div>
                </div>
              </AnimatedSection>
            </div>
          </div>
        </div>
      </section>

      {/* Related Packages */}
      <section className="py-20 safari-pattern-bg relative overflow-hidden">
        {/* Decorative elements */}
        <div className="absolute top-0 right-0 w-64 h-64 bg-jarthaz-gold/5 rounded-full -translate-y-1/2 translate-x-1/2"></div>
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-jarthaz-gold/5 rounded-full translate-y-1/2 -translate-x-1/2"></div>

        <div className="container relative">
          <AnimatedSection>
            <div className="flex flex-col items-center text-center mb-12">
              <div className="inline-block bg-jarthaz-gold/10 px-4 py-1 rounded-full text-jarthaz-gold text-sm font-medium mb-4">
                More Adventures
              </div>
              <h2 className="section-heading text-4xl">You Might Also Like</h2>
              <div className="safari-divider w-24 my-6"></div>
            </div>
          </AnimatedSection>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <AnimatedSection delay={0.2}>
              <div className="bg-white dark:bg-jarthaz-black/50 rounded-lg overflow-hidden shadow-md card-hover h-full flex flex-col">
                <div className="relative h-48 image-hover-zoom">
                  <Image
                    src="https://images.unsplash.com/photo-1516426122078-c23e76319801?fm=jpg&q=80&w=2000&ixlib=rb-4.1.0"
                    alt="African elephants in savanna - Wildlife Safari"
                    fill
                    className="object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
                </div>

                <div className="p-5 flex-grow flex flex-col">
                  <h3 className="font-bold text-lg mb-2">Wildlife Safaris</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Experience Uganda's diverse wildlife in Queen Elizabeth and Murchison Falls National Parks.
                  </p>

                  {/* Package details */}
                  <div className="flex flex-wrap gap-3 mb-4 mt-auto">
                    <div className="flex items-center text-xs text-muted-foreground">
                      <Clock size={14} className="mr-1 text-jarthaz-gold" />
                      <span>4-7 days</span>
                    </div>
                    <div className="flex items-center text-xs text-muted-foreground">
                      <Users size={14} className="mr-1 text-jarthaz-gold" />
                      <span>2-10 people</span>
                    </div>
                  </div>

                  <Button variant="safari" size="sm" className="w-full" asChild>
                    <Link href="/packages/wildlife-safaris" target="_blank">
                      View Package
                    </Link>
                  </Button>
                </div>
              </div>
            </AnimatedSection>

            <AnimatedSection delay={0.3}>
              <div className="bg-white dark:bg-jarthaz-black/50 rounded-lg overflow-hidden shadow-md card-hover h-full flex flex-col">
                <div className="relative h-48 image-hover-zoom">
                  <Image
                    src="https://images.unsplash.com/photo-1515657834497-26509e295154?fm=jpg&q=80&w=2000&ixlib=rb-4.1.0"
                    alt="Traditional African dancers - Cultural Tour"
                    fill
                    className="object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
                </div>

                <div className="p-5 flex-grow flex flex-col">
                  <h3 className="font-bold text-lg mb-2">Cultural Tours</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Immerse yourself in Uganda's rich cultural heritage and traditions.
                  </p>

                  {/* Package details */}
                  <div className="flex flex-wrap gap-3 mb-4 mt-auto">
                    <div className="flex items-center text-xs text-muted-foreground">
                      <Clock size={14} className="mr-1 text-jarthaz-gold" />
                      <span>3-6 days</span>
                    </div>
                    <div className="flex items-center text-xs text-muted-foreground">
                      <Users size={14} className="mr-1 text-jarthaz-gold" />
                      <span>2-15 people</span>
                    </div>
                  </div>

                  <Button variant="safari" size="sm" className="w-full" asChild>
                    <Link href="/packages/cultural-tours" target="_blank">
                      View Package
                    </Link>
                  </Button>
                </div>
              </div>
            </AnimatedSection>

            <AnimatedSection delay={0.4}>
              <div className="bg-white dark:bg-jarthaz-black/50 rounded-lg overflow-hidden shadow-md card-hover h-full flex flex-col">
                <div className="relative h-48 image-hover-zoom">
                  <Image
                    src="https://images.unsplash.com/photo-1540979388789-6cee28a1cdc9?fm=jpg&q=80&w=2000&ixlib=rb-4.1.0"
                    alt="Chimpanzee in forest - Chimpanzee Tracking"
                    fill
                    className="object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
                </div>

                <div className="p-5 flex-grow flex flex-col">
                  <h3 className="font-bold text-lg mb-2">Chimpanzee Tracking</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Track our closest relatives in Kibale Forest National Park.
                  </p>

                  {/* Package details */}
                  <div className="flex flex-wrap gap-3 mb-4 mt-auto">
                    <div className="flex items-center text-xs text-muted-foreground">
                      <Clock size={14} className="mr-1 text-jarthaz-gold" />
                      <span>1-3 days</span>
                    </div>
                    <div className="flex items-center text-xs text-muted-foreground">
                      <Users size={14} className="mr-1 text-jarthaz-gold" />
                      <span>2-8 people</span>
                    </div>
                  </div>

                  <Button variant="safari" size="sm" className="w-full" asChild>
                    <Link href="/contact" target="_blank">
                      Inquire Now
                    </Link>
                  </Button>
                </div>
              </div>
            </AnimatedSection>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-jarthaz-black text-white relative overflow-hidden">
        <div className="absolute inset-0 z-0">
          <Image
            src="/images/gorillas/gorilla-family.jpeg"
            alt="Mountain gorilla family in Uganda"
            fill
            className="object-cover opacity-30"
          />
        </div>

        {/* Decorative elements */}
        <div className="absolute top-0 left-0 w-full h-12 bg-gradient-to-b from-background to-transparent z-10"></div>
        <div className="absolute bottom-0 left-0 w-full h-12 bg-gradient-to-t from-background to-transparent z-10"></div>
        <div className="absolute top-12 right-12 w-24 h-24 border-r-2 border-t-2 border-jarthaz-gold/30 z-10"></div>
        <div className="absolute bottom-12 left-12 w-24 h-24 border-l-2 border-b-2 border-jarthaz-gold/30 z-10"></div>

        <div className="container text-center relative z-10">
          <AnimatedSection>
            <div className="inline-block bg-jarthaz-gold/20 backdrop-blur-sm px-4 py-1 rounded-full text-white text-sm font-medium mb-6">
              Book Your Adventure
            </div>
            <h2 className="text-3xl md:text-4xl font-bold tracking-tight mb-4 text-jarthaz-gold">
              Ready for an Unforgettable Gorilla Experience?
            </h2>
            <div className="safari-divider w-24 mx-auto my-6"></div>
            <p className="mb-8 max-w-2xl mx-auto text-white/90">
              Contact us today to book your gorilla trekking adventure. Our team will help you plan every detail of your
              journey.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" variant="safari" asChild className="btn-shimmer">
                <Link href="/contact">Book Now</Link>
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="bg-white/10 backdrop-blur-sm border-white/20 text-white hover:bg-white/20 hover:text-white"
                asChild
              >
                <Link href="/destinations">Explore Other Packages</Link>
              </Button>
            </div>
          </AnimatedSection>
        </div>
      </section>
    </div>
  )
}
