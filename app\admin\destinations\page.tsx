import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { MapPin, Plus, Search, Edit, Trash2 } from "lucide-react"
import Link from "next/link"
import Image from "next/image"

// Mock data for destinations
const destinations = [
  {
    id: "bwindi",
    name: "Bwindi Impenetrable National Park",
    type: "National Park",
    image: "/placeholder.svg?height=400&width=600",
    description:
      "Home to half of the world's mountain gorilla population, this UNESCO World Heritage Site promises a magical encounter with nature.",
  },
  {
    id: "queen-elizabeth",
    name: "Queen Elizabeth National Park",
    type: "National Park",
    image: "/placeholder.svg?height=400&width=600",
    description: "Uganda's most popular savannah park, offering diverse ecosystems and incredible wildlife.",
  },
  {
    id: "murchison-falls",
    name: "Murchison Falls National Park",
    type: "National Park",
    image: "/placeholder.svg?height=400&width=600",
    description:
      "Witness the dramatic power of the Nile as it forces its way through a narrow gorge, creating the world-famous falls.",
  },
  {
    id: "kidepo-valley",
    name: "Kidepo Valley National Park",
    type: "National Park",
    image: "/placeholder.svg?height=400&width=600",
    description: "A hidden gem in Uganda, known for its rugged beauty and untouched wilderness.",
  },
  {
    id: "lake-bunyonyi",
    name: "Lake Bunyonyi",
    type: "Lake",
    image: "/placeholder.svg?height=400&width=600",
    description:
      'Known as the "place of many little birds," this lake boasts serene beauty and is dotted with 29 islands.',
  },
  {
    id: "jinja",
    name: "Jinja - The Source of the Nile",
    type: "Adventure Hub",
    image: "/placeholder.svg?height=400&width=600",
    description: "The adventure capital of East Africa, where the world's longest river begins.",
  },
]

export default function DestinationsPage() {
  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Destinations</h2>
        <Button asChild>
          <Link href="/admin/destinations/new">
            <Plus className="mr-2 h-4 w-4" /> Add Destination
          </Link>
        </Button>
      </div>

      <div className="flex items-center gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input type="search" placeholder="Search destinations..." className="pl-8" />
        </div>
        <Button variant="outline">Filter</Button>
      </div>

      <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {destinations.map((destination) => (
          <Card key={destination.id} className="overflow-hidden">
            <div className="relative h-48">
              <Image
                src={destination.image || "/placeholder.svg"}
                alt={destination.name}
                fill
                className="object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
              <div className="absolute bottom-4 left-4 flex items-center">
                <MapPin className="mr-1 h-4 w-4 text-jarthaz-gold" />
                <span className="text-sm text-white">{destination.type}</span>
              </div>
            </div>
            <CardHeader>
              <CardTitle className="line-clamp-1">{destination.name}</CardTitle>
              <CardDescription className="line-clamp-2">{destination.description}</CardDescription>
            </CardHeader>
            <CardFooter className="flex justify-between">
              <Button variant="outline" size="sm" asChild>
                <Link href={`/admin/destinations/${destination.id}`}>
                  <Edit className="mr-2 h-4 w-4" /> Edit
                </Link>
              </Button>
              <Button variant="destructive" size="sm">
                <Trash2 className="mr-2 h-4 w-4" /> Delete
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  )
}
